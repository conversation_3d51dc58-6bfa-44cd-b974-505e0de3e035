{% extends 'base.html' %}

{% block head %}
<!-- Custom CSS for Calculette Mauricette -->
<style>
.calculette-mauricette-tool {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 2rem;
    margin: 2rem 0;
    color: #333;
    box-shadow: 0 4px 15px rgba(0,0,0,0.08);
}

.calculator-form {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    border: 1px solid #dee2e6;
}

.time-input-group {
    margin-bottom: 1.5rem;
}

.time-input-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #495057;
    font-size: 0.95rem;
}

.time-input-group input, .time-input-group select {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 1rem;
    background: white;
    transition: border-color 0.3s ease;
}

.time-input-group input:focus, .time-input-group select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

.calculate-btn {
    background: #007bff;
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 6px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
    margin: 1.5rem 0;
}

.calculate-btn:hover {
    background: #0056b3;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,123,255,0.3);
}

.result-display {
    background: #e7f3ff;
    border: 2px solid #b3d9ff;
    border-radius: 8px;
    padding: 1.5rem;
    margin-top: 1.5rem;
    text-align: center;
}

.result-display h3 {
    margin-bottom: 1rem;
    color: #0056b3;
    font-size: 1.2rem;
}

.result-display p {
    margin: 0.5rem 0;
    font-size: 1.1rem;
    font-weight: 500;
    color: #333;
}

.advanced-options {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 1rem;
    margin: 1rem 0;
    border: 1px solid #e9ecef;
}

.advanced-options h4 {
    font-size: 1rem;
    margin-bottom: 1rem;
    color: #495057;
}

.btn-secondary {
    background: #6c757d;
    border-color: #6c757d;
}

.btn-secondary:hover {
    background: #545b62;
    border-color: #545b62;
}

.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4rem 0;
}

.feature-card {
    transition: transform 0.3s ease;
    border: none;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.feature-card:hover {
    transform: translateY(-5px);
}

.comparison-table {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.faq-section .accordion-item {
    border: none;
    margin-bottom: 1rem;
    border-radius: 10px !important;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}
</style>
{% endblock %}

{% block title %}Calculette Mauricette - Calculatrice d'Heures de Travail Gratuite en Ligne{% endblock %}

{% block description %}Calculette Mauricette gratuite pour calculer vos heures de travail avec pause. Calculatrice en ligne simple et efficace pour le calcul des minutes et heures.{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="hero-section text-center" id="hero">
  <div class="container">
    <h1 class="display-4 fw-bold mb-4">Calculette Mauricette - Votre Calculatrice d'Heures de Travail</h1>
    <p class="lead mb-4">La <strong>Calculette Mauricette</strong> est un outil gratuit en ligne qui vous permet de calculer facilement vos heures de travail, pauses incluses. Cette calculatrice d'heure simple et intuitive vous aide à gérer votre temps de travail efficacement.</p>
    <div class="d-flex justify-content-center gap-3 flex-wrap">
      <a href="#calculette" class="btn btn-light btn-lg px-4 py-2">
        <i class="fas fa-calculator me-2"></i>Utiliser la Calculette Mauricette
      </a>
      <a href="#guide" class="btn btn-outline-light btn-lg px-4 py-2">
        <i class="fas fa-question-circle me-2"></i>Guide d'utilisation
      </a>
    </div>
  </div>
</section>

<!-- Pourquoi utiliser section -->
<section class="py-5 bg-light">
  <div class="container">
    <div class="text-center mb-5">
      <h2 class="fw-bold">Pourquoi utiliser notre Calculette Mauricette ?</h2>
      <p class="lead">Notre <strong>calculette mauricette gratuit</strong> offre une solution rapide pour :</p>
    </div>

    <div class="row g-4">
      <div class="col-md-6 col-lg-3">
        <div class="card feature-card h-100 text-center p-4">
          <div class="mb-3">
            <i class="fas fa-clock text-primary" style="font-size: 3rem;"></i>
          </div>
          <h4 class="h5 mb-3">Calculer les heures de travail avec précision</h4>
          <p class="text-muted">Obtenez des calculs d'heures précis au minute près</p>
        </div>
      </div>

      <div class="col-md-6 col-lg-3">
        <div class="card feature-card h-100 text-center p-4">
          <div class="mb-3">
            <i class="fas fa-pause-circle text-primary" style="font-size: 3rem;"></i>
          </div>
          <h4 class="h5 mb-3">Gérer les pauses automatiquement</h4>
          <p class="text-muted">Déduction automatique des temps de pause</p>
        </div>
      </div>

      <div class="col-md-6 col-lg-3">
        <div class="card feature-card h-100 text-center p-4">
          <div class="mb-3">
            <i class="fas fa-bolt text-primary" style="font-size: 3rem;"></i>
          </div>
          <h4 class="h5 mb-3">Obtenir des résultats instantanés</h4>
          <p class="text-muted">Calculs en temps réel sans attente</p>
        </div>
      </div>

      <div class="col-md-6 col-lg-3">
        <div class="card feature-card h-100 text-center p-4">
          <div class="mb-3">
            <i class="fas fa-gift text-primary" style="font-size: 3rem;"></i>
          </div>
          <h4 class="h5 mb-3">Utiliser un outil 100% gratuit</h4>
          <p class="text-muted">Accès libre et illimité à la calculette</p>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Calculette Mauricette Tool Section -->
<section id="calculette" class="py-5">
  <div class="container">
    <div class="calculette-mauricette-tool">
      <h2 class="text-center mb-4"><i class="fas fa-clock me-2"></i>Calculette Mauricette - Calcul d'Heures</h2>

      <div class="calculator-form">
        <!-- Inputs principaux -->
        <div class="row g-3">
          <div class="col-md-4">
            <div class="time-input-group">
              <label for="start-time">Heure de début :</label>
              <input type="time" id="start-time" value="09:00">
            </div>
          </div>

          <div class="col-md-4">
            <div class="time-input-group">
              <label for="end-time">Heure de fin :</label>
              <input type="time" id="end-time" value="17:00">
            </div>
          </div>

          <div class="col-md-4">
            <div class="time-input-group">
              <label for="break-time">Durée de pause (minutes) :</label>
              <input type="number" id="break-time" value="60" min="0" max="480">
            </div>
          </div>
        </div>

        <!-- Options avancées -->
        <div class="advanced-options">
          <h4><i class="fas fa-cog me-2"></i>Options avancées</h4>
          <div class="row g-3">
            <div class="col-md-6">
              <div class="time-input-group">
                <label for="hourly-rate">Taux horaire (€) :</label>
                <input type="number" id="hourly-rate" step="0.01" min="0" placeholder="Ex: 15.50">
              </div>
            </div>
            <div class="col-md-6">
              <div class="time-input-group">
                <label for="work-days">Nombre de jours :</label>
                <input type="number" id="work-days" value="1" min="1" max="31">
              </div>
            </div>
          </div>

          <div class="row g-3">
            <div class="col-md-6">
              <div class="time-input-group">
                <label for="overtime-threshold">Seuil heures sup. (heures) :</label>
                <input type="number" id="overtime-threshold" value="8" min="1" max="12" step="0.5">
              </div>
            </div>
            <div class="col-md-6">
              <div class="time-input-group">
                <label for="overtime-rate">Majoration heures sup. (%) :</label>
                <input type="number" id="overtime-rate" value="25" min="0" max="100">
              </div>
            </div>
          </div>
        </div>

        <!-- Boutons d'action -->
        <div class="row g-2">
          <div class="col-md-8">
            <button onclick="calculateTime()" class="calculate-btn">
              <i class="fas fa-calculator me-2"></i>Calculer avec Calculette Mauricette
            </button>
          </div>
          <div class="col-md-4">
            <button onclick="resetCalculator()" class="btn btn-secondary w-100" style="padding: 1rem;">
              <i class="fas fa-undo me-2"></i>Réinitialiser
            </button>
          </div>
        </div>

        <!-- Résultats détaillés -->
        <div class="result-display" id="result">
          <h3><i class="fas fa-chart-line me-2"></i>Résultats détaillés :</h3>
          <div class="row g-3">
            <div class="col-md-6">
              <p id="total-hours"><strong>Temps de travail :</strong> --</p>
              <p id="total-minutes"><strong>Total en minutes :</strong> --</p>
              <p id="overtime-hours"><strong>Heures supplémentaires :</strong> --</p>
            </div>
            <div class="col-md-6">
              <p id="daily-salary"><strong>Salaire journalier :</strong> --</p>
              <p id="total-salary"><strong>Salaire total :</strong> --</p>
              <p id="average-per-day"><strong>Moyenne par jour :</strong> --</p>
            </div>
          </div>

          <!-- Boutons d'export -->
          <div class="mt-3">
            <button onclick="exportResults()" class="btn btn-outline-primary me-2">
              <i class="fas fa-download me-2"></i>Exporter
            </button>
            <button onclick="shareResults()" class="btn btn-outline-success">
              <i class="fas fa-share me-2"></i>Partager
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
<!-- Guide d'utilisation Section -->
<section id="guide" class="py-5 bg-light">
  <div class="container">
    <div class="text-center mb-5">
      <h2 class="fw-bold">Comment utiliser la Calculette Mauricette ?</h2>
      <p class="lead">Suivez ces étapes simples pour calculer vos heures de travail</p>
    </div>

    <div class="row g-4">
      <div class="col-md-4">
        <div class="card feature-card h-100 text-center p-4">
          <div class="mb-3">
            <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center mx-auto" style="width: 60px; height: 60px;">
              <span class="h4 mb-0">1</span>
            </div>
          </div>
          <h4 class="h5 mb-3">Saisir l'heure de début</h4>
          <p class="text-muted">Entrez votre <strong>heure de début de travail</strong> dans le premier champ de la calculette mauricette.</p>
        </div>
      </div>

      <div class="col-md-4">
        <div class="card feature-card h-100 text-center p-4">
          <div class="mb-3">
            <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center mx-auto" style="width: 60px; height: 60px;">
              <span class="h4 mb-0">2</span>
            </div>
          </div>
          <h4 class="h5 mb-3">Indiquer l'heure de fin</h4>
          <p class="text-muted">Renseignez votre <strong>heure de fin de travail</strong> pour que la calculatrice d'heure puisse effectuer le calcul.</p>
        </div>
      </div>

      <div class="col-md-4">
        <div class="card feature-card h-100 text-center p-4">
          <div class="mb-3">
            <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center mx-auto" style="width: 60px; height: 60px;">
              <span class="h4 mb-0">3</span>
            </div>
          </div>
          <h4 class="h5 mb-3">Ajouter les pauses</h4>
          <p class="text-muted">Précisez la <strong>durée totale de vos pauses</strong> en minutes pour un calcul précis avec notre calculette mauricette.</p>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Avantages Section -->
<section class="py-5 bg-white">
  <div class="container">
    <div class="text-center mb-5">
      <h2 class="fw-bold">Avantages de la Calculette Mauricette</h2>
      <p class="lead">Découvrez pourquoi notre calculatrice d'heure est la meilleure solution</p>
    </div>

    <div class="row g-4">
      <div class="col-md-6">
        <div class="d-flex align-items-start">
          <div class="flex-shrink-0">
            <i class="fas fa-check-circle text-success fs-3 me-3"></i>
          </div>
          <div>
            <h4 class="h5 mb-2">Calcul Précis des Minutes</h4>
            <p class="text-muted">Notre <strong>calculatrice d'heure de travail avec pause</strong> calcule automatiquement vos heures en tenant compte des interruptions.</p>
          </div>
        </div>
      </div>

      <div class="col-md-6">
        <div class="d-flex align-items-start">
          <div class="flex-shrink-0">
            <i class="fas fa-check-circle text-success fs-3 me-3"></i>
          </div>
          <div>
            <h4 class="h5 mb-2">Interface Simple et Intuitive</h4>
            <p class="text-muted">La <strong>calculette mauricette</strong> offre une interface claire, accessible à tous les utilisateurs.</p>
          </div>
        </div>
      </div>

      <div class="col-md-6">
        <div class="d-flex align-items-start">
          <div class="flex-shrink-0">
            <i class="fas fa-check-circle text-success fs-3 me-3"></i>
          </div>
          <div>
            <h4 class="h5 mb-2">Résultats Instantanés</h4>
            <p class="text-muted">Obtenez immédiatement le résultat de votre <strong>calcul des minutes</strong> et heures de travail.</p>
          </div>
        </div>
      </div>

      <div class="col-md-6">
        <div class="d-flex align-items-start">
          <div class="flex-shrink-0">
            <i class="fas fa-check-circle text-success fs-3 me-3"></i>
          </div>
          <div>
            <h4 class="h5 mb-2">Outil 100% Gratuit</h4>
            <p class="text-muted">Notre <strong>calculette mauricette gratuit</strong> est accessible sans inscription ni frais.</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Applications Section -->
<section class="py-5 bg-light">
  <div class="container">
    <div class="text-center mb-5">
      <h2 class="fw-bold">Applications de la Calculette Mauricette</h2>
      <p class="lead">Découvrez comment notre calculatrice d'heure peut vous aider dans différents contextes</p>
    </div>

    <div class="row g-4">
      <div class="col-md-6 col-lg-4">
        <div class="card h-100 shadow-sm feature-card">
          <div class="card-body p-4">
            <div class="mb-3 text-center">
              <i class="fas fa-user-tie text-primary" style="font-size: 3rem;"></i>
            </div>
            <h3 class="h5 mb-3 text-center">Pour les Employés</h3>
            <ul class="list-unstyled">
              <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Vérifier ses heures de travail quotidiennes</li>
              <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Calculer les heures supplémentaires</li>
              <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Suivre son temps de travail hebdomadaire</li>
            </ul>
          </div>
        </div>
      </div>

      <div class="col-md-6 col-lg-4">
        <div class="card h-100 shadow-sm feature-card">
          <div class="card-body p-4">
            <div class="mb-3 text-center">
              <i class="fas fa-laptop text-primary" style="font-size: 3rem;"></i>
            </div>
            <h3 class="h5 mb-3 text-center">Pour les Freelances</h3>
            <ul class="list-unstyled">
              <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Facturer précisément le temps passé</li>
              <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Gérer plusieurs projets simultanément</li>
              <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Optimiser sa productivité</li>
            </ul>
          </div>
        </div>
      </div>

      <div class="col-md-6 col-lg-4">
        <div class="card h-100 shadow-sm feature-card">
          <div class="card-body p-4">
            <div class="mb-3 text-center">
              <i class="fas fa-users text-primary" style="font-size: 3rem;"></i>
            </div>
            <h3 class="h5 mb-3 text-center">Pour les Gestionnaires RH</h3>
            <ul class="list-unstyled">
              <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Contrôler les heures des équipes</li>
              <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Calculer les salaires horaires</li>
              <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Gérer les plannings de travail</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Comparison Section -->
<section class="py-5 bg-white">
  <div class="container">
    <div class="text-center mb-5">
      <h2 class="fw-bold">Calculette Mauricette vs Autres Outils</h2>
      <p class="lead">Découvrez pourquoi notre calculatrice d'heure est supérieure</p>
    </div>

    <div class="row justify-content-center">
      <div class="col-lg-10">
        <div class="comparison-table">
          <div class="table-responsive">
            <table class="table table-striped table-hover mb-0">
              <thead class="table-primary">
                <tr>
                  <th scope="col">Fonctionnalité</th>
                  <th scope="col" class="text-center">Calculette Mauricette</th>
                  <th scope="col" class="text-center">Autres calculatrices</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td><strong>Gratuit</strong></td>
                  <td class="text-center"><i class="fas fa-check text-success fs-5"></i> Oui</td>
                  <td class="text-center"><i class="fas fa-times text-danger fs-5"></i> Souvent payant</td>
                </tr>
                <tr>
                  <td><strong>Calcul avec pause</strong></td>
                  <td class="text-center"><i class="fas fa-check text-success fs-5"></i> Automatique</td>
                  <td class="text-center"><i class="fas fa-times text-danger fs-5"></i> Manuel</td>
                </tr>
                <tr>
                  <td><strong>Interface simple</strong></td>
                  <td class="text-center"><i class="fas fa-check text-success fs-5"></i> Très intuitive</td>
                  <td class="text-center"><i class="fas fa-times text-danger fs-5"></i> Complexe</td>
                </tr>
                <tr>
                  <td><strong>Résultats instantanés</strong></td>
                  <td class="text-center"><i class="fas fa-check text-success fs-5"></i> Immédiat</td>
                  <td class="text-center"><i class="fas fa-times text-danger fs-5"></i> Lent</td>
                </tr>
                <tr>
                  <td><strong>Accessible en ligne</strong></td>
                  <td class="text-center"><i class="fas fa-check text-success fs-5"></i> 24h/24</td>
                  <td class="text-center"><i class="fas fa-times text-danger fs-5"></i> Limité</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <div class="text-center mt-4">
      <a href="#calculette" class="btn btn-primary btn-lg">
        <i class="fas fa-calculator me-2"></i>Essayer la Calculette Mauricette
      </a>
    </div>
  </div>
</section>

<!-- Liens Utiles Section -->
<section class="py-5 bg-light">
  <div class="container">
    <div class="text-center mb-5">
      <h2 class="fw-bold">Liens Utiles - Calculette Mauricette</h2>
      <p class="lead">Découvrez nos outils spécialisés et ressources complémentaires</p>
    </div>

    <div class="row g-4">
      <div class="col-md-6 col-lg-3">
        <div class="card h-100 shadow-sm feature-card">
          <div class="card-body p-4 text-center">
            <div class="mb-3">
              <i class="fas fa-clock text-primary" style="font-size: 3rem;"></i>
            </div>
            <h3 class="h5 mb-3">Calculette Mauricette Heure</h3>
            <p class="card-text">Calcul d'heures détaillé avec fonctionnalités avancées</p>
            <div class="mt-auto pt-3">
              <a href="#" class="btn btn-outline-primary d-block">Accéder</a>
            </div>
          </div>
        </div>
      </div>

      <div class="col-md-6 col-lg-3">
        <div class="card h-100 shadow-sm feature-card">
          <div class="card-body p-4 text-center">
            <div class="mb-3">
              <i class="fas fa-calendar-week text-primary" style="font-size: 3rem;"></i>
            </div>
            <h3 class="h5 mb-3">Calculette Mauricette Semaine</h3>
            <p class="card-text">Calcul hebdomadaire pour planifier votre semaine</p>
            <div class="mt-auto pt-3">
              <a href="/calculette-semaine" class="btn btn-outline-primary d-block">Accéder</a>
            </div>
          </div>
        </div>
      </div>

      <div class="col-md-6 col-lg-3">
        <div class="card h-100 shadow-sm feature-card">
          <div class="card-body p-4 text-center">
            <div class="mb-3">
              <i class="fas fa-calendar-alt text-primary" style="font-size: 3rem;"></i>
            </div>
            <h3 class="h5 mb-3">Calculette Mauricette Mois</h3>
            <p class="card-text">Calcul mensuel pour une vue d'ensemble</p>
            <div class="mt-auto pt-3">
              <a href="/calculette-mois" class="btn btn-outline-primary d-block">Accéder</a>
            </div>
          </div>
        </div>
      </div>

      <div class="col-md-6 col-lg-3">
        <div class="card h-100 shadow-sm feature-card">
          <div class="card-body p-4 text-center">
            <div class="mb-3">
              <i class="fas fa-file-excel text-primary" style="font-size: 3rem;"></i>
            </div>
            <h3 class="h5 mb-3">Calculette Mauricette Excel</h3>
            <p class="card-text">Templates Excel gratuits à télécharger</p>
            <div class="mt-auto pt-3">
              <a href="#" class="btn btn-outline-primary d-block">Télécharger</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- FAQ Section -->
<section class="py-5 bg-white faq-section">
  <div class="container">
    <div class="text-center mb-5">
      <h2 class="fw-bold">Questions Fréquentes - Calculette Mauricette</h2>
      <p class="lead">Trouvez les réponses aux questions les plus courantes sur notre calculatrice d'heure</p>
    </div>

    <div class="row justify-content-center">
      <div class="col-lg-10">
        <div class="accordion" id="faqAccordion">
          <div class="accordion-item shadow-sm mb-3">
            <h3 class="accordion-header" id="headingOne">
              <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
                Qu'est-ce que la Calculette Mauricette exactement ?
              </button>
            </h3>
            <div id="collapseOne" class="accordion-collapse collapse show" aria-labelledby="headingOne" data-bs-parent="#faqAccordion">
              <div class="accordion-body">
                <p>La <strong>Calculette Mauricette</strong> est une calculatrice en ligne spécialisée dans le calcul d'heures de travail. Elle permet de calculer précisément votre temps de travail en déduisant automatiquement les pauses.</p>
                <p>Notre outil est conçu pour être simple, rapide et précis, offrant une solution complète pour tous vos besoins de calcul d'heures.</p>
              </div>
            </div>
          </div>

          <div class="accordion-item shadow-sm mb-3">
            <h3 class="accordion-header" id="headingTwo">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
                La Calculette Mauricette est-elle vraiment gratuite ?
              </button>
            </h3>
            <div id="collapseTwo" class="accordion-collapse collapse" aria-labelledby="headingTwo" data-bs-parent="#faqAccordion">
              <div class="accordion-body">
                <p>Oui, notre <strong>calculette mauricette gratuit</strong> est entièrement gratuite et accessible sans inscription. Vous pouvez l'utiliser autant de fois que nécessaire.</p>
                <p>Nous croyons que les outils de calcul d'heures de travail doivent être accessibles à tous, c'est pourquoi la Calculette Mauricette restera toujours gratuite.</p>
              </div>
            </div>
          </div>

          <div class="accordion-item shadow-sm mb-3">
            <h3 class="accordion-header" id="headingThree">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
                Comment la Calculette Mauricette gère-t-elle les pauses ?
              </button>
            </h3>
            <div id="collapseThree" class="accordion-collapse collapse" aria-labelledby="headingThree" data-bs-parent="#faqAccordion">
              <div class="accordion-body">
                <p>Notre <strong>calculatrice d'heure de travail avec pause</strong> soustrait automatiquement la durée des pauses du temps total pour vous donner le temps de travail effectif.</p>
                <p>Il suffit d'indiquer la durée totale de vos pauses en minutes, et la Calculette Mauricette s'occupe du reste automatiquement.</p>
              </div>
            </div>
          </div>

          <div class="accordion-item shadow-sm mb-3">
            <h3 class="accordion-header" id="headingFour">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseFour" aria-expanded="false" aria-controls="collapseFour">
                Puis-je utiliser la Calculette Mauricette pour calculer une semaine complète ?
              </button>
            </h3>
            <div id="collapseFour" class="accordion-collapse collapse" aria-labelledby="headingFour" data-bs-parent="#faqAccordion">
              <div class="accordion-body">
                <p>Oui, vous pouvez utiliser notre <strong>calculette mauricette semaine</strong> pour calculer chaque jour et additionner les résultats. Consultez notre page dédiée au calcul hebdomadaire.</p>
                <p>Pour plus de commodité, nous proposons également des outils spécialisés pour le calcul hebdomadaire et mensuel.</p>
              </div>
            </div>
          </div>

          <div class="accordion-item shadow-sm">
            <h3 class="accordion-header" id="headingFive">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseFive" aria-expanded="false" aria-controls="collapseFive">
                Existe-t-il une version Excel de la Calculette Mauricette ?
              </button>
            </h3>
            <div id="collapseFive" class="accordion-collapse collapse" aria-labelledby="headingFive" data-bs-parent="#faqAccordion">
              <div class="accordion-body">
                <p>Oui, nous proposons des templates <strong>calculette mauricette excel</strong> téléchargeables gratuitement sur notre page dédiée.</p>
                <p>Ces feuilles de calcul Excel vous permettent de gérer vos heures de travail hors ligne avec les mêmes fonctionnalités que notre outil en ligne.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- CTA Section -->
<section class="py-5 bg-primary text-white">
  <div class="container">
    <div class="row py-3 justify-content-center text-center">
      <div class="col-lg-8">
        <h2 class="fw-bold mb-3">Prêt à utiliser la Calculette Mauricette ?</h2>
        <p class="lead mb-4">Utilisez notre calculatrice d'heure gratuite pour calculer vos heures de travail avec pause en quelques clics !</p>
        <a href="#calculette" class="btn btn-light btn-lg px-4 py-2">
          <i class="fas fa-calculator me-2"></i>Calculer mes heures maintenant
        </a>
      </div>
    </div>
  </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
// Fonction de calcul avancée pour la Calculette Mauricette
function calculateTime() {
  const startTime = document.getElementById('start-time').value;
  const endTime = document.getElementById('end-time').value;
  const breakMinutes = parseInt(document.getElementById('break-time').value) || 0;
  const hourlyRate = parseFloat(document.getElementById('hourly-rate').value) || 0;
  const workDays = parseInt(document.getElementById('work-days').value) || 1;
  const overtimeThreshold = parseFloat(document.getElementById('overtime-threshold').value) || 8;
  const overtimeRate = parseFloat(document.getElementById('overtime-rate').value) || 25;

  if (!startTime || !endTime) {
    alert('Veuillez saisir les heures de début et de fin');
    return;
  }

  const start = new Date(`2024-01-01T${startTime}`);
  const end = new Date(`2024-01-01T${endTime}`);

  let diffMs = end - start;
  if (diffMs < 0) {
    diffMs += 24 * 60 * 60 * 1000; // Add 24 hours for next day
  }

  const totalMinutes = Math.floor(diffMs / (1000 * 60)) - breakMinutes;
  const dailyHours = totalMinutes / 60;
  const hours = Math.floor(totalMinutes / 60);
  const minutes = totalMinutes % 60;

  // Calcul des heures supplémentaires
  const overtimeHours = Math.max(0, dailyHours - overtimeThreshold);
  const regularHours = Math.min(dailyHours, overtimeThreshold);

  // Calculs salariaux
  let dailySalary = 0;
  let totalSalary = 0;
  let averagePerDay = 0;

  if (hourlyRate > 0) {
    const overtimeHourlyRate = hourlyRate * (1 + overtimeRate / 100);
    dailySalary = (regularHours * hourlyRate) + (overtimeHours * overtimeHourlyRate);
    totalSalary = dailySalary * workDays;
    averagePerDay = totalSalary / workDays;
  }

  // Affichage des résultats
  document.getElementById('total-hours').innerHTML = `<strong>Temps de travail :</strong> ${hours}h ${minutes}min`;
  document.getElementById('total-minutes').innerHTML = `<strong>Total en minutes :</strong> ${totalMinutes} minutes`;
  document.getElementById('overtime-hours').innerHTML = `<strong>Heures supplémentaires :</strong> ${overtimeHours.toFixed(2)}h`;

  if (hourlyRate > 0) {
    document.getElementById('daily-salary').innerHTML = `<strong>Salaire journalier :</strong> ${dailySalary.toFixed(2)}€`;
    document.getElementById('total-salary').innerHTML = `<strong>Salaire total :</strong> ${totalSalary.toFixed(2)}€`;
    document.getElementById('average-per-day').innerHTML = `<strong>Moyenne par jour :</strong> ${averagePerDay.toFixed(2)}€`;
  } else {
    document.getElementById('daily-salary').innerHTML = `<strong>Salaire journalier :</strong> --`;
    document.getElementById('total-salary').innerHTML = `<strong>Salaire total :</strong> --`;
    document.getElementById('average-per-day').innerHTML = `<strong>Moyenne par jour :</strong> --`;
  }

  // Stocker les résultats pour l'export
  window.calculationResults = {
    startTime, endTime, breakMinutes, totalMinutes, hours, minutes,
    overtimeHours, dailySalary, totalSalary, workDays, hourlyRate
  };
}

// Fonction de réinitialisation
function resetCalculator() {
  document.getElementById('start-time').value = '09:00';
  document.getElementById('end-time').value = '17:00';
  document.getElementById('break-time').value = '60';
  document.getElementById('hourly-rate').value = '';
  document.getElementById('work-days').value = '1';
  document.getElementById('overtime-threshold').value = '8';
  document.getElementById('overtime-rate').value = '25';

  document.getElementById('total-hours').innerHTML = '<strong>Temps de travail :</strong> --';
  document.getElementById('total-minutes').innerHTML = '<strong>Total en minutes :</strong> --';
  document.getElementById('overtime-hours').innerHTML = '<strong>Heures supplémentaires :</strong> --';
  document.getElementById('daily-salary').innerHTML = '<strong>Salaire journalier :</strong> --';
  document.getElementById('total-salary').innerHTML = '<strong>Salaire total :</strong> --';
  document.getElementById('average-per-day').innerHTML = '<strong>Moyenne par jour :</strong> --';
}

// Fonction d'export
function exportResults() {
  if (!window.calculationResults) {
    alert('Veuillez d\'abord effectuer un calcul');
    return;
  }

  const results = window.calculationResults;
  const exportData = `Calculette Mauricette - Résultats
=====================================
Heure de début: ${results.startTime}
Heure de fin: ${results.endTime}
Pause: ${results.breakMinutes} minutes
Temps de travail: ${results.hours}h ${results.minutes}min
Total en minutes: ${results.totalMinutes}
Heures supplémentaires: ${results.overtimeHours.toFixed(2)}h
${results.hourlyRate > 0 ? `Salaire journalier: ${results.dailySalary.toFixed(2)}€` : ''}
${results.hourlyRate > 0 ? `Salaire total (${results.workDays} jours): ${results.totalSalary.toFixed(2)}€` : ''}

Généré le ${new Date().toLocaleString('fr-FR')}
`;

  const blob = new Blob([exportData], { type: 'text/plain' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `calculette-mauricette-${new Date().toISOString().split('T')[0]}.txt`;
  a.click();
  URL.revokeObjectURL(url);
}

// Fonction de partage
function shareResults() {
  if (!window.calculationResults) {
    alert('Veuillez d\'abord effectuer un calcul');
    return;
  }

  const results = window.calculationResults;
  const shareText = `J'ai calculé mes heures de travail avec la Calculette Mauricette : ${results.hours}h ${results.minutes}min de travail effectif !`;

  if (navigator.share) {
    navigator.share({
      title: 'Mes résultats Calculette Mauricette',
      text: shareText,
      url: window.location.href
    });
  } else {
    navigator.clipboard.writeText(shareText + ' ' + window.location.href)
      .then(() => {
        alert('Résultats copiés dans le presse-papier !');
      });
  }
}

// Smooth scrolling pour les liens d'ancrage
document.addEventListener('DOMContentLoaded', function() {
  // Smooth scrolling pour tous les liens d'ancrage
  const anchorLinks = document.querySelectorAll('a[href^="#"]');

  anchorLinks.forEach(link => {
    link.addEventListener('click', function(e) {
      e.preventDefault();

      const targetId = this.getAttribute('href').substring(1);
      const targetElement = document.getElementById(targetId);

      if (targetElement) {
        targetElement.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }
    });
  });

  // Animation au scroll pour les cartes
  const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
  };

  const observer = new IntersectionObserver(function(entries) {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.style.opacity = '1';
        entry.target.style.transform = 'translateY(0)';
      }
    });
  }, observerOptions);

  // Observer toutes les cartes
  const cards = document.querySelectorAll('.feature-card');
  cards.forEach(card => {
    card.style.opacity = '0';
    card.style.transform = 'translateY(20px)';
    card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
    observer.observe(card);
  });
});
</script>
{% endblock %}