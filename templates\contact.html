{% extends 'base.html' %}

{% block title %}Contact - Calculette Mauricette{% endblock %}

{% block description %}Contactez l'équipe de Calculette Mauricette pour toute question ou suggestion. Nous sommes là pour vous aider avec notre calculatrice d'heures de travail gratuite.{% endblock %}

{% block schema_data %}
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "ContactPage",
  "name": "Contact - Calculette Mauricette",
  "description": "Contactez l'équipe de Calculette Mauricette pour toute question ou suggestion",
  "url": "https://calculettemauricette.blog/contact",
  "mainEntity": {
    "@type": "Organization",
    "name": "Calculette Mauricette",
    "email": "<EMAIL>",
    "url": "https://calculettemauricette.blog"
  }
}
</script>
{% endblock %}

{% block content %}
<!-- Breadcrumb Navigation -->
<nav aria-label="breadcrumb" class="bg-light py-2">
    <div class="container">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item"><a href="/" class="text-decoration-none">Accueil</a></li>
            <li class="breadcrumb-item active" aria-current="page">Contact</li>
        </ol>
    </div>
</nav>

<!-- Contact Section -->
<section class="py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="text-center mb-5">
                    <h1 class="fw-bold mb-3">Contactez-nous</h1>
                    <p class="lead text-muted">Une question ? Une suggestion ? N'hésitez pas à nous contacter !</p>
                </div>

                <div class="card shadow-sm">
                    <div class="card-body p-4">
                        <div class="row g-4">
                            <!-- Contact Information -->
                            <div class="col-md-5">
                                <h2 class="h4 mb-4">Nos coordonnées</h2>
                                <div class="d-flex align-items-center mb-3">
                                    <i class="fas fa-envelope text-primary me-3 fs-4"></i>
                                    <div>
                                        <h3 class="h6 mb-1">Email</h3>
                                        <a href="mailto:<EMAIL>" class="text-decoration-none"><EMAIL></a>
                                    </div>
                                </div>
                                <div class="d-flex align-items-center mb-3">
                                    <i class="fas fa-globe text-primary me-3 fs-4"></i>
                                    <div>
                                        <h3 class="h6 mb-1">Site web</h3>
                                        <a href="https://calculettemauricette.blog" class="text-decoration-none">calculettemauricette.blog</a>
                                    </div>
                                </div>
                                <div class="mt-4">
                                    <h3 class="h6 mb-3">Suivez-nous</h3>
                                    <div class="d-flex gap-3">
                                        <a href="#" class="text-decoration-none text-primary fs-4"><i class="fab fa-facebook"></i></a>
                                        <a href="#" class="text-decoration-none text-primary fs-4"><i class="fab fa-twitter"></i></a>
                                        <a href="#" class="text-decoration-none text-primary fs-4"><i class="fab fa-linkedin"></i></a>
                                    </div>
                                </div>
                            </div>

                            <!-- Contact Form -->
                            <div class="col-md-7">
                                <h2 class="h4 mb-4">Envoyez-nous un message</h2>
                                <form id="contactForm" onsubmit="return handleSubmit(event)">
                                    <div class="mb-3">
                                        <label for="name" class="form-label">Nom</label>
                                        <input type="text" class="form-control" id="name" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="email" class="form-label">Email</label>
                                        <input type="email" class="form-control" id="email" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="subject" class="form-label">Sujet</label>
                                        <input type="text" class="form-control" id="subject" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="message" class="form-label">Message</label>
                                        <textarea class="form-control" id="message" rows="4" required></textarea>
                                    </div>
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="fas fa-paper-plane me-2"></i>Envoyer le message
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- FAQ Section -->
                <div class="mt-5">
                    <h2 class="h4 mb-4">Questions fréquentes</h2>
                    <div class="accordion" id="faqAccordion">
                        <div class="accordion-item">
                            <h3 class="accordion-header">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                    Comment utiliser la Calculette Mauricette ?
                                </button>
                            </h3>
                            <div id="faq1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    Consultez notre <a href="/guide-calculette-mauricette" class="text-decoration-none">guide d'utilisation</a> pour apprendre à utiliser la Calculette Mauricette efficacement.
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <h3 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                                    La Calculette Mauricette est-elle gratuite ?
                                </button>
                            </h3>
                            <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    Oui, la Calculette Mauricette est entièrement gratuite et accessible sans inscription.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
function handleSubmit(event) {
    event.preventDefault();
    
    // Récupérer les valeurs du formulaire
    const formData = {
        name: document.getElementById('name').value,
        email: document.getElementById('email').value,
        subject: document.getElementById('subject').value,
        message: document.getElementById('message').value
    };

    // Ici, vous pouvez ajouter le code pour envoyer les données à votre backend
    console.log('Form data:', formData);
    
    // Afficher un message de confirmation
    alert('Merci pour votre message ! Nous vous répondrons dans les plus brefs délais.');
    
    // Réinitialiser le formulaire
    document.getElementById('contactForm').reset();
    
    return false;
}
</script>
{% endblock %} 