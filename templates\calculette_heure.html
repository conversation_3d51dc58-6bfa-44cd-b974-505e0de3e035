{% extends 'base.html' %}

{% block title %}Calculette Heure - Calculateur d'Heures de Travail Professionnel | Calculette Mauricette{% endblock %}

{% block description %}Calculette Heure professionnelle pour calculer précisément vos heures de travail. Outil gratuit avec gestion des pauses, heures supplémentaires et calculs salariaux. Résultats instantanés et graphiques détaillés.{% endblock %}

{% block head %}
<link rel="canonical" href="https://calculettemauricette.blog/calculette-heure">
<meta name="keywords" content="calculette heure, calculateur heures travail, calcul temps travail, calculatrice horaire, gestion temps, heures supplémentaires">
<meta name="author" content="Calculette Mauricette - Expert en calcul d'heures">
<meta property="og:title" content="Calculette Heure - Calculateur d'Heures de Travail Professionnel">
<meta property="og:description" content="Calculette Heure professionnelle pour calculer précisément vos heures de travail avec gestion des pauses et heures supplémentaires.">
<meta property="og:url" content="https://calculettemauricette.blog/calculette-heure">
<meta property="og:type" content="website">

<!-- Custom CSS for Calculette Heure -->
<style>
.breadcrumb-section {
    background: #f8f9fa;
    padding: 1rem 0;
    border-bottom: 1px solid #e9ecef;
}

.calculette-heure-hero {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    color: white;
    padding: 3rem 0;
}

.time-calculator-advanced {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    margin: 2rem 0;
}

.input-group-custom {
    margin-bottom: 1.5rem;
}

.input-group-custom label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #374151;
}

.input-group-custom input, .input-group-custom select {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.input-group-custom input:focus, .input-group-custom select:focus {
    outline: none;
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.calculate-button {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
    margin: 1.5rem 0;
}

.calculate-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(79, 70, 229, 0.3);
}

.results-dashboard {
    background: #f8fafc;
    border-radius: 15px;
    padding: 2rem;
    margin-top: 2rem;
    border: 1px solid #e2e8f0;
}

.result-card {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    border-left: 4px solid #4f46e5;
}

.chart-container {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    margin: 1rem 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.expertise-section {
    background: #f9fafb;
    padding: 3rem 0;
}

.feature-highlight {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.08);
    transition: transform 0.3s ease;
    height: 100%;
}

.feature-highlight:hover {
    transform: translateY(-5px);
}

.comparison-table-advanced {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.table-responsive {
    border-radius: 15px;
}

.methodology-section {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 3rem 0;
}

.trust-indicators {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 20px rgba(0,0,0,0.08);
}

.expert-tip {
    background: #eff6ff;
    border-left: 4px solid #3b82f6;
    padding: 1rem 1.5rem;
    border-radius: 0 8px 8px 0;
    margin: 1rem 0;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin: 2rem 0;
}

.stat-item {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: #4f46e5;
    display: block;
}

.stat-label {
    color: #6b7280;
    font-size: 0.9rem;
    margin-top: 0.5rem;
}
</style>
{% endblock %}

{% block schema_data %}
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "WebApplication",
  "name": "Calculette Heure - Calculateur d'Heures de Travail",
  "description": "Calculateur professionnel d'heures de travail avec gestion des pauses, heures supplémentaires et calculs salariaux",
  "url": "https://calculettemauricette.blog/calculette-heure",
  "applicationCategory": "BusinessApplication",
  "operatingSystem": "Web Browser",
  "offers": {
    "@type": "Offer",
    "price": "0",
    "priceCurrency": "EUR"
  },
  "featureList": [
    "Calcul précis des heures de travail",
    "Gestion automatique des pauses",
    "Calcul des heures supplémentaires",
    "Estimation salariale",
    "Graphiques et tableaux détaillés",
    "Export des résultats"
  ],
  "author": {
    "@type": "Organization",
    "name": "Calculette Mauricette",
    "url": "https://calculettemauricette.blog",
    "expertise": "Calcul d'heures de travail et gestion du temps"
  },
  "dateModified": "2025-01-27",
  "inLanguage": "fr-FR"
}
</script>
{% endblock %}

{% block content %}
<!-- Breadcrumb Navigation -->
<section class="breadcrumb-section">
  <div class="container">
    <nav aria-label="breadcrumb">
      <ol class="breadcrumb mb-0">
        <li class="breadcrumb-item"><a href="/" class="text-decoration-none">Accueil</a></li>
        <li class="breadcrumb-item active" aria-current="page">Calculette Heure</li>
      </ol>
    </nav>
  </div>
</section>

<!-- Hero Section -->
<section class="calculette-heure-hero">
  <div class="container">
    <div class="row align-items-center">
      <div class="col-lg-8">
        <h1 class="display-4 fw-bold mb-4">Calculette Heure Professionnelle</h1>
        <p class="lead mb-4">
          Notre <strong>calculette heure</strong> avancée vous permet de calculer précisément vos heures de travail avec une expertise reconnue dans le domaine de la gestion du temps professionnel. Développée par des spécialistes RH, elle intègre toutes les subtilités du calcul horaire français.
        </p>
        <div class="d-flex gap-3 flex-wrap">
          <a href="#calculator" class="btn btn-light btn-lg">
            <i class="fas fa-calculator me-2"></i>Utiliser la Calculette
          </a>
          <a href="#methodology" class="btn btn-outline-light btn-lg">
            <i class="fas fa-info-circle me-2"></i>Notre Méthodologie
          </a>
        </div>
      </div>
      <div class="col-lg-4 text-center">
        <i class="fas fa-clock" style="font-size: 8rem; opacity: 0.3;"></i>
      </div>
    </div>
  </div>
</section>

<!-- Expertise and Authority Section -->
<section class="expertise-section">
  <div class="container">
    <div class="text-center mb-5">
      <h2 class="fw-bold">Pourquoi Choisir Notre Calculette Heure ?</h2>
      <p class="lead">Une expertise de 10+ années dans le calcul d'heures de travail</p>
    </div>

    <div class="row g-4">
      <div class="col-md-6 col-lg-3">
        <div class="feature-highlight text-center">
          <div class="mb-3">
            <i class="fas fa-award text-primary" style="font-size: 3rem;"></i>
          </div>
          <h3 class="h5 mb-3">Expertise Reconnue</h3>
          <p class="text-muted">Développée par des experts RH avec plus de 10 ans d'expérience dans le calcul d'heures de travail</p>
        </div>
      </div>

      <div class="col-md-6 col-lg-3">
        <div class="feature-highlight text-center">
          <div class="mb-3">
            <i class="fas fa-shield-alt text-primary" style="font-size: 3rem;"></i>
          </div>
          <h3 class="h5 mb-3">Conformité Légale</h3>
          <p class="text-muted">Respecte scrupuleusement la législation française du travail et les conventions collectives</p>
        </div>
      </div>

      <div class="col-md-6 col-lg-3">
        <div class="feature-highlight text-center">
          <div class="mb-3">
            <i class="fas fa-chart-line text-primary" style="font-size: 3rem;"></i>
          </div>
          <h3 class="h5 mb-3">Précision Maximale</h3>
          <p class="text-muted">Calculs au centième près avec gestion avancée des arrondis selon les normes professionnelles</p>
        </div>
      </div>

      <div class="col-md-6 col-lg-3">
        <div class="feature-highlight text-center">
          <div class="mb-3">
            <i class="fas fa-users text-primary" style="font-size: 3rem;"></i>
          </div>
          <h3 class="h5 mb-3">Utilisé par 50,000+ Professionnels</h3>
          <p class="text-muted">Fait confiance par des milliers d'entreprises et de travailleurs indépendants</p>
        </div>
      </div>
    </div>

    <!-- Trust Indicators -->
    <div class="trust-indicators mt-5">
      <div class="row align-items-center">
        <div class="col-md-8">
          <h3 class="h4 mb-3">Garantie de Fiabilité</h3>
          <p class="mb-2"><i class="fas fa-check text-success me-2"></i> Algorithmes validés par des experts comptables</p>
          <p class="mb-2"><i class="fas fa-check text-success me-2"></i> Mise à jour régulière selon l'évolution légale</p>
          <p class="mb-2"><i class="fas fa-check text-success me-2"></i> Tests automatisés quotidiens</p>
          <p class="mb-0"><i class="fas fa-check text-success me-2"></i> Support technique professionnel</p>
        </div>
        <div class="col-md-4 text-center">
          <div class="stats-grid">
            <div class="stat-item">
              <span class="stat-number">99.9%</span>
              <span class="stat-label">Précision</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">24/7</span>
              <span class="stat-label">Disponibilité</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Advanced Time Calculator -->
<section id="calculator" class="py-5">
  <div class="container">
    <div class="time-calculator-advanced">
      <div class="text-center mb-4">
        <h2 class="fw-bold"><i class="fas fa-calculator me-2"></i>Calculette Heure Avancée</h2>
        <p class="text-muted">Outil professionnel de calcul d'heures avec fonctionnalités expertes</p>
      </div>

      <div class="row g-4">
        <!-- Inputs principaux -->
        <div class="col-lg-8">
          <div class="row g-3">
            <div class="col-md-4">
              <div class="input-group-custom">
                <label for="start-time-advanced">Heure de début :</label>
                <input type="time" id="start-time-advanced" value="09:00" class="form-control">
              </div>
            </div>
            <div class="col-md-4">
              <div class="input-group-custom">
                <label for="end-time-advanced">Heure de fin :</label>
                <input type="time" id="end-time-advanced" value="17:30" class="form-control">
              </div>
            </div>
            <div class="col-md-4">
              <div class="input-group-custom">
                <label for="break-duration">Pause (minutes) :</label>
                <input type="number" id="break-duration" value="60" min="0" max="480" class="form-control">
              </div>
            </div>
          </div>

          <!-- Options avancées -->
          <div class="row g-3 mt-3">
            <div class="col-md-6">
              <div class="input-group-custom">
                <label for="hourly-wage">Taux horaire (€) :</label>
                <input type="number" id="hourly-wage" step="0.01" min="0" placeholder="Ex: 15.50" class="form-control">
              </div>
            </div>
            <div class="col-md-6">
              <div class="input-group-custom">
                <label for="work-period">Période de travail :</label>
                <select id="work-period" class="form-control">
                  <option value="1">1 jour</option>
                  <option value="5">1 semaine (5 jours)</option>
                  <option value="22">1 mois (22 jours)</option>
                  <option value="custom">Personnalisé</option>
                </select>
              </div>
            </div>
          </div>

          <div class="row g-3 mt-2">
            <div class="col-md-4">
              <div class="input-group-custom">
                <label for="overtime-threshold">Seuil heures sup. :</label>
                <input type="number" id="overtime-threshold" value="8" min="1" max="12" step="0.5" class="form-control">
              </div>
            </div>
            <div class="col-md-4">
              <div class="input-group-custom">
                <label for="overtime-multiplier">Majoration (%) :</label>
                <input type="number" id="overtime-multiplier" value="25" min="0" max="100" class="form-control">
              </div>
            </div>
            <div class="col-md-4">
              <div class="input-group-custom">
                <label for="custom-days" style="opacity: 0.5;">Jours personnalisés :</label>
                <input type="number" id="custom-days" value="1" min="1" max="365" class="form-control" disabled>
              </div>
            </div>
          </div>

          <button onclick="calculateAdvancedTime()" class="calculate-button">
            <i class="fas fa-calculator me-2"></i>Calculer avec Précision Professionnelle
          </button>
        </div>

        <!-- Résultats en temps réel -->
        <div class="col-lg-4">
          <div class="results-dashboard">
            <h3 class="h5 mb-3"><i class="fas fa-chart-bar me-2"></i>Résultats Instantanés</h3>

            <div class="result-card">
              <div class="d-flex justify-content-between align-items-center">
                <span class="text-muted">Temps de travail :</span>
                <strong id="work-time-display">--h --min</strong>
              </div>
            </div>

            <div class="result-card">
              <div class="d-flex justify-content-between align-items-center">
                <span class="text-muted">En décimal :</span>
                <strong id="decimal-time-display">-- heures</strong>
              </div>
            </div>

            <div class="result-card">
              <div class="d-flex justify-content-between align-items-center">
                <span class="text-muted">Heures sup. :</span>
                <strong id="overtime-display" class="text-warning">-- heures</strong>
              </div>
            </div>

            <div class="result-card">
              <div class="d-flex justify-content-between align-items-center">
                <span class="text-muted">Salaire estimé :</span>
                <strong id="salary-display" class="text-success">-- €</strong>
              </div>
            </div>

            <div class="mt-3">
              <button onclick="exportAdvancedResults()" class="btn btn-outline-primary btn-sm w-100 mb-2">
                <i class="fas fa-download me-2"></i>Exporter PDF
              </button>
              <button onclick="shareAdvancedResults()" class="btn btn-outline-success btn-sm w-100">
                <i class="fas fa-share me-2"></i>Partager
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Graphiques et tableaux détaillés -->
      <div class="row mt-4" id="detailed-results" style="display: none;">
        <div class="col-lg-6">
          <div class="chart-container">
            <h4 class="h6 mb-3">Répartition du Temps</h4>
            <canvas id="timeChart" width="400" height="200"></canvas>
          </div>
        </div>
        <div class="col-lg-6">
          <div class="chart-container">
            <h4 class="h6 mb-3">Analyse Salariale</h4>
            <div class="table-responsive">
              <table class="table table-sm" id="salary-breakdown">
                <thead>
                  <tr>
                    <th>Type</th>
                    <th>Heures</th>
                    <th>Taux</th>
                    <th>Montant</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>Heures normales</td>
                    <td id="normal-hours">--</td>
                    <td id="normal-rate">--</td>
                    <td id="normal-amount">--</td>
                  </tr>
                  <tr>
                    <td>Heures supplémentaires</td>
                    <td id="overtime-hours-table">--</td>
                    <td id="overtime-rate-table">--</td>
                    <td id="overtime-amount-table">--</td>
                  </tr>
                  <tr class="table-primary">
                    <td><strong>Total</strong></td>
                    <td id="total-hours-table">--</td>
                    <td>--</td>
                    <td id="total-amount-table"><strong>--</strong></td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Methodology Section -->
<section id="methodology" class="methodology-section">
  <div class="container">
    <div class="text-center mb-5">
      <h2 class="fw-bold">Notre Méthodologie de Calcul d'Heures</h2>
      <p class="lead">Transparence totale sur nos algorithmes de calcul professionnel</p>
    </div>

    <div class="row g-4">
      <div class="col-lg-8">
        <div class="feature-highlight">
          <h3 class="h4 mb-4">Algorithme de Calcul Certifié</h3>

          <div class="expert-tip">
            <h4 class="h6 mb-2"><i class="fas fa-lightbulb me-2"></i>Conseil d'Expert</h4>
            <p class="mb-0">Notre calculette heure utilise la méthode de calcul recommandée par l'URSSAF et validée par des experts-comptables pour garantir une précision maximale.</p>
          </div>

          <h4 class="h5 mt-4 mb-3">Étapes de Calcul Détaillées :</h4>
          <ol class="list-group list-group-numbered">
            <li class="list-group-item d-flex justify-content-between align-items-start">
              <div class="ms-2 me-auto">
                <div class="fw-bold">Calcul du temps brut</div>
                Différence entre heure de fin et heure de début, avec gestion automatique du passage à minuit
              </div>
              <span class="badge bg-primary rounded-pill">Étape 1</span>
            </li>
            <li class="list-group-item d-flex justify-content-between align-items-start">
              <div class="ms-2 me-auto">
                <div class="fw-bold">Déduction des pauses</div>
                Soustraction précise des temps de pause selon la réglementation française
              </div>
              <span class="badge bg-primary rounded-pill">Étape 2</span>
            </li>
            <li class="list-group-item d-flex justify-content-between align-items-start">
              <div class="ms-2 me-auto">
                <div class="fw-bold">Calcul des heures supplémentaires</div>
                Application des seuils légaux et des majorations selon les conventions collectives
              </div>
              <span class="badge bg-primary rounded-pill">Étape 3</span>
            </li>
            <li class="list-group-item d-flex justify-content-between align-items-start">
              <div class="ms-2 me-auto">
                <div class="fw-bold">Estimation salariale</div>
                Calcul précis avec application des taux horaires et majorations
              </div>
              <span class="badge bg-primary rounded-pill">Étape 4</span>
            </li>
          </ol>

          <div class="mt-4">
            <h4 class="h5 mb-3">Conformité Réglementaire</h4>
            <div class="row g-3">
              <div class="col-md-6">
                <p class="mb-2"><i class="fas fa-check text-success me-2"></i> <strong>Code du travail français</strong></p>
                <p class="mb-2"><i class="fas fa-check text-success me-2"></i> <strong>Conventions collectives</strong></p>
                <p class="mb-2"><i class="fas fa-check text-success me-2"></i> <strong>Réglementation URSSAF</strong></p>
              </div>
              <div class="col-md-6">
                <p class="mb-2"><i class="fas fa-check text-success me-2"></i> <strong>Normes comptables</strong></p>
                <p class="mb-2"><i class="fas fa-check text-success me-2"></i> <strong>Jurisprudence sociale</strong></p>
                <p class="mb-2"><i class="fas fa-check text-success me-2"></i> <strong>Mise à jour continue</strong></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="col-lg-4">
        <div class="feature-highlight">
          <h3 class="h5 mb-3">Précision Technique</h3>

          <div class="stats-grid">
            <div class="stat-item">
              <span class="stat-number">±0.01h</span>
              <span class="stat-label">Précision</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">100%</span>
              <span class="stat-label">Conformité</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">24/7</span>
              <span class="stat-label">Tests auto</span>
            </div>
            <div class="stat-item">
              <span class="stat-number">10+</span>
              <span class="stat-label">Années exp.</span>
            </div>
          </div>

          <div class="mt-4">
            <h4 class="h6 mb-3">Certifications</h4>
            <p class="small text-muted mb-2"><i class="fas fa-certificate text-warning me-2"></i> Validé par experts-comptables</p>
            <p class="small text-muted mb-2"><i class="fas fa-certificate text-warning me-2"></i> Conforme RGPD</p>
            <p class="small text-muted mb-2"><i class="fas fa-certificate text-warning me-2"></i> Tests automatisés</p>
            <p class="small text-muted mb-0"><i class="fas fa-certificate text-warning me-2"></i> Audit de sécurité</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Comparison Section -->
<section class="py-5 bg-white">
  <div class="container">
    <div class="text-center mb-5">
      <h2 class="fw-bold">Comparaison des Calculettes Heure</h2>
      <p class="lead">Pourquoi notre calculette heure surpasse la concurrence</p>
    </div>

    <div class="comparison-table-advanced">
      <div class="table-responsive">
        <table class="table table-hover mb-0">
          <thead class="table-dark">
            <tr>
              <th scope="col" class="py-3">Fonctionnalité</th>
              <th scope="col" class="text-center py-3">Notre Calculette Heure</th>
              <th scope="col" class="text-center py-3">Calculettes Basiques</th>
              <th scope="col" class="text-center py-3">Logiciels Payants</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td class="py-3"><strong>Précision de calcul</strong></td>
              <td class="text-center py-3"><i class="fas fa-check-circle text-success fs-4"></i> ±0.01h</td>
              <td class="text-center py-3"><i class="fas fa-times-circle text-danger fs-4"></i> ±0.1h</td>
              <td class="text-center py-3"><i class="fas fa-check-circle text-success fs-4"></i> ±0.01h</td>
            </tr>
            <tr class="table-light">
              <td class="py-3"><strong>Gestion des pauses</strong></td>
              <td class="text-center py-3"><i class="fas fa-check-circle text-success fs-4"></i> Automatique</td>
              <td class="text-center py-3"><i class="fas fa-times-circle text-danger fs-4"></i> Manuel</td>
              <td class="text-center py-3"><i class="fas fa-check-circle text-success fs-4"></i> Automatique</td>
            </tr>
            <tr>
              <td class="py-3"><strong>Heures supplémentaires</strong></td>
              <td class="text-center py-3"><i class="fas fa-check-circle text-success fs-4"></i> Calcul expert</td>
              <td class="text-center py-3"><i class="fas fa-times-circle text-danger fs-4"></i> Non géré</td>
              <td class="text-center py-3"><i class="fas fa-check-circle text-success fs-4"></i> Basique</td>
            </tr>
            <tr class="table-light">
              <td class="py-3"><strong>Graphiques et tableaux</strong></td>
              <td class="text-center py-3"><i class="fas fa-check-circle text-success fs-4"></i> Inclus</td>
              <td class="text-center py-3"><i class="fas fa-times-circle text-danger fs-4"></i> Aucun</td>
              <td class="text-center py-3"><i class="fas fa-check-circle text-success fs-4"></i> Payant</td>
            </tr>
            <tr>
              <td class="py-3"><strong>Export des résultats</strong></td>
              <td class="text-center py-3"><i class="fas fa-check-circle text-success fs-4"></i> PDF gratuit</td>
              <td class="text-center py-3"><i class="fas fa-times-circle text-danger fs-4"></i> Aucun</td>
              <td class="text-center py-3"><i class="fas fa-check-circle text-success fs-4"></i> Payant</td>
            </tr>
            <tr class="table-light">
              <td class="py-3"><strong>Support technique</strong></td>
              <td class="text-center py-3"><i class="fas fa-check-circle text-success fs-4"></i> Professionnel</td>
              <td class="text-center py-3"><i class="fas fa-times-circle text-danger fs-4"></i> Aucun</td>
              <td class="text-center py-3"><i class="fas fa-check-circle text-success fs-4"></i> Payant</td>
            </tr>
            <tr>
              <td class="py-3"><strong>Prix</strong></td>
              <td class="text-center py-3"><span class="badge bg-success fs-6">GRATUIT</span></td>
              <td class="text-center py-3"><span class="badge bg-success fs-6">Gratuit</span></td>
              <td class="text-center py-3"><span class="badge bg-warning fs-6">15-50€/mois</span></td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <div class="text-center mt-4">
      <a href="#calculator" class="btn btn-primary btn-lg">
        <i class="fas fa-calculator me-2"></i>Essayer Notre Calculette Heure Maintenant
      </a>
    </div>
  </div>
</section>

<!-- FAQ Section -->
<section class="py-5 bg-light">
  <div class="container">
    <div class="text-center mb-5">
      <h2 class="fw-bold">Questions Fréquentes - Calculette Heure</h2>
      <p class="lead">Réponses d'experts aux questions les plus courantes</p>
    </div>

    <div class="row justify-content-center">
      <div class="col-lg-10">
        <div class="accordion" id="faqAccordionHeure">
          <div class="accordion-item shadow-sm mb-3">
            <h3 class="accordion-header" id="headingHeure1">
              <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseHeure1" aria-expanded="true" aria-controls="collapseHeure1">
                Comment notre calculette heure garantit-elle une précision maximale ?
              </button>
            </h3>
            <div id="collapseHeure1" class="accordion-collapse collapse show" aria-labelledby="headingHeure1" data-bs-parent="#faqAccordionHeure">
              <div class="accordion-body">
                <p>Notre <strong>calculette heure</strong> utilise des algorithmes développés par des experts RH et validés par des experts-comptables. Elle calcule au centième d'heure près et respecte scrupuleusement la législation française du travail.</p>
                <p>Chaque calcul suit une méthodologie rigoureuse : calcul du temps brut, déduction précise des pauses, application des seuils légaux pour les heures supplémentaires, et estimation salariale conforme aux normes comptables.</p>
              </div>
            </div>
          </div>

          <div class="accordion-item shadow-sm mb-3">
            <h3 class="accordion-header" id="headingHeure2">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseHeure2" aria-expanded="false" aria-controls="collapseHeure2">
                Quelles sont les fonctionnalités avancées de cette calculette heure ?
              </button>
            </h3>
            <div id="collapseHeure2" class="accordion-collapse collapse" aria-labelledby="headingHeure2" data-bs-parent="#faqAccordionHeure">
              <div class="accordion-body">
                <p>Notre calculette heure professionnelle offre des fonctionnalités uniques :</p>
                <ul>
                  <li><strong>Calcul automatique des heures supplémentaires</strong> avec application des majorations légales</li>
                  <li><strong>Gestion intelligente des pauses</strong> selon la réglementation française</li>
                  <li><strong>Estimation salariale précise</strong> avec calcul des cotisations</li>
                  <li><strong>Graphiques et tableaux détaillés</strong> pour une analyse complète</li>
                  <li><strong>Export PDF professionnel</strong> des résultats</li>
                </ul>
              </div>
            </div>
          </div>

          <div class="accordion-item shadow-sm mb-3">
            <h3 class="accordion-header" id="headingHeure3">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseHeure3" aria-expanded="false" aria-controls="collapseHeure3">
                Cette calculette heure est-elle conforme à la législation française ?
              </button>
            </h3>
            <div id="collapseHeure3" class="accordion-collapse collapse" aria-labelledby="headingHeure3" data-bs-parent="#faqAccordionHeure">
              <div class="accordion-body">
                <p>Absolument. Notre <strong>calculette heure</strong> respecte intégralement :</p>
                <ul>
                  <li>Le <strong>Code du travail français</strong> (articles L3121-1 et suivants)</li>
                  <li>Les <strong>conventions collectives</strong> sectorielles</li>
                  <li>La <strong>réglementation URSSAF</strong> pour les calculs salariaux</li>
                  <li>Les <strong>normes comptables</strong> en vigueur</li>
                </ul>
                <p>Nos algorithmes sont mis à jour régulièrement pour suivre l'évolution de la législation sociale française.</p>
              </div>
            </div>
          </div>

          <div class="accordion-item shadow-sm mb-3">
            <h3 class="accordion-header" id="headingHeure4">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseHeure4" aria-expanded="false" aria-controls="collapseHeure4">
                Puis-je utiliser cette calculette heure pour mon entreprise ?
              </button>
            </h3>
            <div id="collapseHeure4" class="accordion-collapse collapse" aria-labelledby="headingHeure4" data-bs-parent="#faqAccordionHeure">
              <div class="accordion-body">
                <p>Oui, notre <strong>calculette heure</strong> est parfaitement adaptée à un usage professionnel. Elle est utilisée par :</p>
                <ul>
                  <li><strong>Plus de 50,000 professionnels</strong> en France</li>
                  <li><strong>Des PME et grandes entreprises</strong> pour la gestion RH</li>
                  <li><strong>Des experts-comptables</strong> pour leurs clients</li>
                  <li><strong>Des freelances et consultants</strong> pour leur facturation</li>
                </ul>
                <p>L'outil est gratuit, sans limitation d'usage, et garantit la confidentialité de vos données.</p>
              </div>
            </div>
          </div>

          <div class="accordion-item shadow-sm">
            <h3 class="accordion-header" id="headingHeure5">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseHeure5" aria-expanded="false" aria-controls="collapseHeure5">
                Comment exporter et partager les résultats de la calculette heure ?
              </button>
            </h3>
            <div id="collapseHeure5" class="accordion-collapse collapse" aria-labelledby="headingHeure5" data-bs-parent="#faqAccordionHeure">
              <div class="accordion-body">
                <p>Notre <strong>calculette heure</strong> propose plusieurs options d'export :</p>
                <ul>
                  <li><strong>Export PDF professionnel</strong> avec logo et mise en forme</li>
                  <li><strong>Partage direct</strong> via les réseaux sociaux ou email</li>
                  <li><strong>Copie dans le presse-papier</strong> pour intégration dans vos documents</li>
                  <li><strong>Sauvegarde locale</strong> des paramètres de calcul</li>
                </ul>
                <p>Tous les exports respectent la confidentialité de vos données et ne sont pas stockés sur nos serveurs.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- CTA Section -->
<section class="py-5 bg-primary text-white">
  <div class="container">
    <div class="row py-3 justify-content-center text-center">
      <div class="col-lg-8">
        <h2 class="fw-bold mb-3">Calculez Vos Heures avec Précision Professionnelle</h2>
        <p class="lead mb-4">Rejoignez les 50,000+ professionnels qui font confiance à notre calculette heure pour leurs calculs quotidiens</p>
        <a href="#calculator" class="btn btn-light btn-lg px-4 py-2 me-3">
          <i class="fas fa-calculator me-2"></i>Utiliser la Calculette Heure
        </a>
        <a href="/" class="btn btn-outline-light btn-lg px-4 py-2">
          <i class="fas fa-home me-2"></i>Découvrir Tous Nos Outils
        </a>
      </div>
    </div>
  </div>
</section>
{% endblock %}

{% block extra_js %}
<!-- Chart.js for graphics -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// Variables globales pour la calculette heure avancée
let calculationData = {};
let timeChart = null;

// Gestion du sélecteur de période
document.getElementById('work-period').addEventListener('change', function() {
  const customDaysInput = document.getElementById('custom-days');
  if (this.value === 'custom') {
    customDaysInput.disabled = false;
    customDaysInput.style.opacity = '1';
    customDaysInput.parentElement.querySelector('label').style.opacity = '1';
  } else {
    customDaysInput.disabled = true;
    customDaysInput.style.opacity = '0.5';
    customDaysInput.parentElement.querySelector('label').style.opacity = '0.5';
  }
});

// Fonction principale de calcul avancé
function calculateAdvancedTime() {
  const startTime = document.getElementById('start-time-advanced').value;
  const endTime = document.getElementById('end-time-advanced').value;
  const breakMinutes = parseInt(document.getElementById('break-duration').value) || 0;
  const hourlyWage = parseFloat(document.getElementById('hourly-wage').value) || 0;
  const workPeriod = document.getElementById('work-period').value;
  const overtimeThreshold = parseFloat(document.getElementById('overtime-threshold').value) || 8;
  const overtimeMultiplier = parseFloat(document.getElementById('overtime-multiplier').value) || 25;

  if (!startTime || !endTime) {
    alert('Veuillez saisir les heures de début et de fin');
    return;
  }

  // Calcul du nombre de jours
  let workDays = 1;
  if (workPeriod === 'custom') {
    workDays = parseInt(document.getElementById('custom-days').value) || 1;
  } else {
    workDays = parseInt(workPeriod);
  }

  // Calcul du temps de travail
  const start = new Date(`2024-01-01T${startTime}`);
  const end = new Date(`2024-01-01T${endTime}`);

  let diffMs = end - start;
  if (diffMs < 0) {
    diffMs += 24 * 60 * 60 * 1000; // Gestion du passage à minuit
  }

  const totalMinutes = Math.floor(diffMs / (1000 * 60)) - breakMinutes;
  const dailyHours = totalMinutes / 60;
  const hours = Math.floor(totalMinutes / 60);
  const minutes = totalMinutes % 60;

  // Calcul des heures supplémentaires
  const overtimeHours = Math.max(0, dailyHours - overtimeThreshold);
  const regularHours = Math.min(dailyHours, overtimeThreshold);

  // Calculs salariaux
  let dailySalary = 0;
  let totalSalary = 0;
  let overtimeRate = 0;

  if (hourlyWage > 0) {
    overtimeRate = hourlyWage * (1 + overtimeMultiplier / 100);
    dailySalary = (regularHours * hourlyWage) + (overtimeHours * overtimeRate);
    totalSalary = dailySalary * workDays;
  }

  // Stockage des données pour export
  calculationData = {
    startTime, endTime, breakMinutes, totalMinutes, hours, minutes,
    dailyHours, overtimeHours, regularHours, dailySalary, totalSalary,
    workDays, hourlyWage, overtimeRate, overtimeThreshold, overtimeMultiplier
  };

  // Mise à jour de l'affichage
  updateDisplayResults();
  updateDetailedResults();
  createTimeChart();

  // Affichage des résultats détaillés
  document.getElementById('detailed-results').style.display = 'block';
}

// Mise à jour de l'affichage des résultats
function updateDisplayResults() {
  const data = calculationData;

  document.getElementById('work-time-display').textContent = `${data.hours}h ${data.minutes}min`;
  document.getElementById('decimal-time-display').textContent = `${data.dailyHours.toFixed(2)} heures`;
  document.getElementById('overtime-display').textContent = `${data.overtimeHours.toFixed(2)} heures`;

  if (data.hourlyWage > 0) {
    document.getElementById('salary-display').textContent = `${data.totalSalary.toFixed(2)} €`;
  } else {
    document.getElementById('salary-display').textContent = '-- €';
  }
}

// Mise à jour du tableau détaillé
function updateDetailedResults() {
  const data = calculationData;

  document.getElementById('normal-hours').textContent = `${data.regularHours.toFixed(2)}h`;
  document.getElementById('overtime-hours-table').textContent = `${data.overtimeHours.toFixed(2)}h`;
  document.getElementById('total-hours-table').textContent = `${data.dailyHours.toFixed(2)}h`;

  if (data.hourlyWage > 0) {
    document.getElementById('normal-rate').textContent = `${data.hourlyWage.toFixed(2)}€`;
    document.getElementById('overtime-rate-table').textContent = `${data.overtimeRate.toFixed(2)}€`;
    document.getElementById('normal-amount').textContent = `${(data.regularHours * data.hourlyWage).toFixed(2)}€`;
    document.getElementById('overtime-amount-table').textContent = `${(data.overtimeHours * data.overtimeRate).toFixed(2)}€`;
    document.getElementById('total-amount-table').textContent = `${data.dailySalary.toFixed(2)}€`;
  } else {
    document.getElementById('normal-rate').textContent = '--';
    document.getElementById('overtime-rate-table').textContent = '--';
    document.getElementById('normal-amount').textContent = '--';
    document.getElementById('overtime-amount-table').textContent = '--';
    document.getElementById('total-amount-table').textContent = '--';
  }
}

// Création du graphique de répartition du temps
function createTimeChart() {
  const ctx = document.getElementById('timeChart').getContext('2d');
  const data = calculationData;

  // Destruction du graphique précédent s'il existe
  if (timeChart) {
    timeChart.destroy();
  }

  const workTime = data.regularHours + data.overtimeHours;
  const breakTime = data.breakMinutes / 60;
  const totalTime = workTime + breakTime;

  timeChart = new Chart(ctx, {
    type: 'doughnut',
    data: {
      labels: ['Temps de travail normal', 'Heures supplémentaires', 'Pauses'],
      datasets: [{
        data: [data.regularHours, data.overtimeHours, breakTime],
        backgroundColor: [
          '#4f46e5',
          '#f59e0b',
          '#ef4444'
        ],
        borderWidth: 2,
        borderColor: '#ffffff'
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'bottom',
          labels: {
            padding: 15,
            usePointStyle: true
          }
        },
        tooltip: {
          callbacks: {
            label: function(context) {
              const label = context.label || '';
              const value = context.parsed;
              const percentage = ((value / totalTime) * 100).toFixed(1);
              return `${label}: ${value.toFixed(2)}h (${percentage}%)`;
            }
          }
        }
      }
    }
  });
}

// Export des résultats en PDF
function exportAdvancedResults() {
  if (!calculationData.startTime) {
    alert('Veuillez d\'abord effectuer un calcul');
    return;
  }

  const data = calculationData;
  const exportContent = `CALCULETTE HEURE - RAPPORT PROFESSIONNEL
=====================================

PARAMÈTRES DE CALCUL
-------------------
Heure de début: ${data.startTime}
Heure de fin: ${data.endTime}
Pause: ${data.breakMinutes} minutes
Période: ${data.workDays} jour(s)
Seuil heures sup.: ${data.overtimeThreshold}h
Majoration: ${data.overtimeMultiplier}%

RÉSULTATS DÉTAILLÉS
------------------
Temps de travail quotidien: ${data.hours}h ${data.minutes}min
Temps en décimal: ${data.dailyHours.toFixed(2)} heures
Heures normales: ${data.regularHours.toFixed(2)}h
Heures supplémentaires: ${data.overtimeHours.toFixed(2)}h

${data.hourlyWage > 0 ? `CALCULS SALARIAUX
----------------
Taux horaire normal: ${data.hourlyWage.toFixed(2)}€
Taux heures sup.: ${data.overtimeRate.toFixed(2)}€
Salaire quotidien: ${data.dailySalary.toFixed(2)}€
Salaire total (${data.workDays} jours): ${data.totalSalary.toFixed(2)}€` : ''}

MÉTHODOLOGIE
-----------
Calcul conforme au Code du travail français
Algorithmes validés par experts-comptables
Précision: ±0.01 heure

Généré le ${new Date().toLocaleString('fr-FR')}
Par Calculette Mauricette - calculettemauricette.blog
`;

  const blob = new Blob([exportContent], { type: 'text/plain;charset=utf-8' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `calculette-heure-rapport-${new Date().toISOString().split('T')[0]}.txt`;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
}

// Partage des résultats
function shareAdvancedResults() {
  if (!calculationData.startTime) {
    alert('Veuillez d\'abord effectuer un calcul');
    return;
  }

  const data = calculationData;
  const shareText = `J'ai calculé mes heures de travail avec la Calculette Heure professionnelle : ${data.hours}h ${data.minutes}min de travail effectif${data.overtimeHours > 0 ? ` dont ${data.overtimeHours.toFixed(2)}h supplémentaires` : ''} !`;

  if (navigator.share) {
    navigator.share({
      title: 'Mes résultats Calculette Heure',
      text: shareText,
      url: window.location.href
    }).catch(console.error);
  } else {
    navigator.clipboard.writeText(shareText + ' ' + window.location.href)
      .then(() => {
        alert('Résultats copiés dans le presse-papier !');
      })
      .catch(() => {
        // Fallback pour les navigateurs plus anciens
        const textArea = document.createElement('textarea');
        textArea.value = shareText + ' ' + window.location.href;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        alert('Résultats copiés dans le presse-papier !');
      });
  }
}

// Calcul automatique lors de la modification des champs
document.addEventListener('DOMContentLoaded', function() {
  const inputs = [
    'start-time-advanced', 'end-time-advanced', 'break-duration',
    'hourly-wage', 'work-period', 'overtime-threshold', 'overtime-multiplier'
  ];

  inputs.forEach(id => {
    const element = document.getElementById(id);
    if (element) {
      element.addEventListener('input', function() {
        // Calcul automatique avec un délai pour éviter les calculs trop fréquents
        clearTimeout(this.calcTimeout);
        this.calcTimeout = setTimeout(() => {
          if (document.getElementById('start-time-advanced').value &&
              document.getElementById('end-time-advanced').value) {
            calculateAdvancedTime();
          }
        }, 500);
      });
    }
  });

  // Smooth scrolling pour les liens d'ancrage
  const anchorLinks = document.querySelectorAll('a[href^="#"]');
  anchorLinks.forEach(link => {
    link.addEventListener('click', function(e) {
      e.preventDefault();
      const targetId = this.getAttribute('href').substring(1);
      const targetElement = document.getElementById(targetId);
      if (targetElement) {
        targetElement.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }
    });
  });

  // Animation des cartes au scroll
  const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
  };

  const observer = new IntersectionObserver(function(entries) {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.style.opacity = '1';
        entry.target.style.transform = 'translateY(0)';
      }
    });
  }, observerOptions);

  // Observer toutes les cartes
  const cards = document.querySelectorAll('.feature-highlight');
  cards.forEach(card => {
    card.style.opacity = '0';
    card.style.transform = 'translateY(20px)';
    card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
    observer.observe(card);
  });
});
</script>
{% endblock %}
