{% extends 'base.html' %}

{% block head %}
<!-- Custom CSS for Calculette Semaine -->
<style>
.calculette-semaine-tool {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 2rem;
    margin: 2rem 0;
    color: #333;
    box-shadow: 0 4px 15px rgba(0,0,0,0.08);
}

.week-calculator-form {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    border: 1px solid #dee2e6;
}

.day-input-group {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    border: 1px solid #e9ecef;
}

.day-input-group h5 {
    color: #495057;
    margin-bottom: 1rem;
    font-weight: 600;
}

.time-input {
    width: 100%;
    padding: 0.5rem;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 0.9rem;
    background: white;
    transition: border-color 0.3s ease;
}

.time-input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

.calculate-week-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 6px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
    margin: 1.5rem 0;
}

.calculate-week-btn:hover {
    background: #218838;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(40,167,69,0.3);
}

.week-results {
    background: #e8f5e8;
    border: 2px solid #c3e6c3;
    border-radius: 8px;
    padding: 1.5rem;
    margin-top: 1.5rem;
}

.week-summary-table {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.chart-container {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    margin: 1rem 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.breadcrumb {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 2rem;
}

.expert-tip {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 1rem;
    margin: 1rem 0;
}

.expert-tip .icon {
    color: #856404;
    font-size: 1.2rem;
}

.feature-highlight {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
    padding: 2rem;
    margin: 2rem 0;
}

.stats-card {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-5px);
}

.stats-number {
    font-size: 2.5rem;
    font-weight: bold;
    color: #007bff;
}

.methodology-section {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 2rem;
    margin: 2rem 0;
}
</style>
{% endblock %}

{% block title %}Calculette Semaine - Calculateur d'Heures de Travail Hebdomadaire Gratuit{% endblock %}

{% block description %}Calculette Semaine gratuite pour calculer vos heures de travail hebdomadaires. Outil professionnel de calcul d'heures avec analyse détaillée et graphiques.{% endblock %}

{% block schema_data %}
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "WebApplication",
  "name": "Calculette Semaine - Calculateur Hebdomadaire",
  "description": "Calculateur d'heures de travail hebdomadaire professionnel avec analyse détaillée",
  "url": "https://calculettemauricette.blog/calculette-semaine",
  "applicationCategory": "BusinessApplication",
  "operatingSystem": "Web Browser",
  "offers": {
    "@type": "Offer",
    "price": "0",
    "priceCurrency": "EUR"
  },
  "featureList": [
    "Calcul hebdomadaire d'heures",
    "Analyse graphique",
    "Gestion des pauses par jour",
    "Calcul des heures supplémentaires",
    "Export des résultats"
  ],
  "author": {
    "@type": "Organization",
    "name": "Calculette Mauricette",
    "url": "https://calculettemauricette.blog"
  }
}
</script>
{% endblock %}

{% block content %}
<!-- Breadcrumb Navigation -->
<div class="container mt-3">
  <nav aria-label="breadcrumb">
    <ol class="breadcrumb">
      <li class="breadcrumb-item"><a href="/"><i class="fas fa-home"></i> Accueil</a></li>
      <li class="breadcrumb-item active" aria-current="page"><i class="fas fa-calendar-week"></i> Calculette Semaine</li>
    </ol>
  </nav>
</div>

<!-- Hero Section -->
<section class="feature-highlight">
  <div class="container">
    <div class="text-center">
      <h1 class="display-4 fw-bold mb-4">
        <i class="fas fa-calendar-week me-3"></i>Calculette Semaine - Calculateur d'Heures Hebdomadaire
      </h1>
      <p class="lead mb-4">
        La <strong>Calculette Semaine</strong> est un outil professionnel gratuit qui vous permet de calculer précisément vos heures de travail hebdomadaires. 
        Cette calculatrice hebdomadaire avancée analyse jour par jour votre temps de travail, gère automatiquement les pauses et génère des rapports détaillés 
        avec graphiques pour optimiser votre gestion du temps.
      </p>
      <div class="d-flex justify-content-center gap-3 flex-wrap">
        <a href="#calculette-semaine" class="btn btn-light btn-lg px-4 py-2">
          <i class="fas fa-calculator me-2"></i>Utiliser la Calculette Semaine
        </a>
        <a href="#methodologie" class="btn btn-outline-light btn-lg px-4 py-2">
          <i class="fas fa-chart-line me-2"></i>Voir la Méthodologie
        </a>
      </div>
    </div>
  </div>
</section>

<!-- Introduction Expert Section -->
<section class="py-5 bg-white">
  <div class="container">
    <div class="row align-items-center">
      <div class="col-lg-8">
        <h2 class="fw-bold mb-4">Pourquoi utiliser notre Calculette Semaine professionnelle ?</h2>
        <p class="lead">
          En tant qu'experts en gestion du temps de travail, nous avons développé cette <strong>calculette semaine</strong> 
          pour répondre aux besoins spécifiques des professionnels, gestionnaires RH et freelances qui nécessitent 
          un suivi précis de leurs heures hebdomadaires.
        </p>
        <p>
          Notre calculateur hebdomadaire intègre les meilleures pratiques de l'industrie et respecte les réglementations 
          françaises du travail. Contrairement aux calculatrices basiques, notre outil offre une analyse approfondie 
          avec visualisations graphiques, détection automatique des heures supplémentaires et calculs de productivité.
        </p>
        
        <div class="expert-tip">
          <div class="d-flex align-items-start">
            <i class="fas fa-lightbulb icon me-3 mt-1"></i>
            <div>
              <strong>Conseil d'Expert :</strong> Une gestion précise des heures hebdomadaires permet d'améliorer 
              la productivité de 15% en moyenne selon les études de l'ANACT (Agence Nationale pour l'Amélioration 
              des Conditions de Travail).
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-4">
        <div class="stats-card">
          <div class="stats-number">7</div>
          <h4>Jours analysés</h4>
          <p class="text-muted">Suivi complet de votre semaine de travail</p>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Calculette Semaine Tool Section -->
<section id="calculette-semaine" class="py-5 bg-light">
  <div class="container">
    <div class="calculette-semaine-tool">
      <h2 class="text-center mb-4">
        <i class="fas fa-calendar-week me-2"></i>Calculette Semaine - Outil de Calcul Hebdomadaire
      </h2>

      <div class="week-calculator-form">
        <!-- Configuration générale -->
        <div class="row mb-4">
          <div class="col-md-6">
            <label for="week-start-date" class="form-label fw-bold">Date de début de semaine :</label>
            <input type="date" id="week-start-date" class="form-control">
          </div>
          <div class="col-md-3">
            <label for="default-break" class="form-label fw-bold">Pause par défaut (min) :</label>
            <input type="number" id="default-break" class="form-control" value="60" min="0" max="480">
          </div>
          <div class="col-md-3">
            <label for="hourly-rate-week" class="form-label fw-bold">Taux horaire (€) :</label>
            <input type="number" id="hourly-rate-week" class="form-control" step="0.01" min="0" placeholder="15.50">
          </div>
        </div>

        <!-- Jours de la semaine -->
        <div class="row">
          <div class="col-12">
            <h4 class="mb-3"><i class="fas fa-clock me-2"></i>Saisie des horaires par jour</h4>
          </div>
        </div>

        <!-- Lundi -->
        <div class="day-input-group">
          <h5><i class="fas fa-calendar-day me-2"></i>Lundi</h5>
          <div class="row g-2">
            <div class="col-md-3">
              <label class="form-label small">Début :</label>
              <input type="time" class="time-input" id="monday-start" value="09:00">
            </div>
            <div class="col-md-3">
              <label class="form-label small">Fin :</label>
              <input type="time" class="time-input" id="monday-end" value="17:00">
            </div>
            <div class="col-md-3">
              <label class="form-label small">Pause (min) :</label>
              <input type="number" class="time-input" id="monday-break" value="60" min="0">
            </div>
            <div class="col-md-3">
              <div class="form-check mt-4">
                <input class="form-check-input" type="checkbox" id="monday-worked" checked>
                <label class="form-check-label" for="monday-worked">Jour travaillé</label>
              </div>
            </div>
          </div>
        </div>

        <!-- Mardi -->
        <div class="day-input-group">
          <h5><i class="fas fa-calendar-day me-2"></i>Mardi</h5>
          <div class="row g-2">
            <div class="col-md-3">
              <label class="form-label small">Début :</label>
              <input type="time" class="time-input" id="tuesday-start" value="09:00">
            </div>
            <div class="col-md-3">
              <label class="form-label small">Fin :</label>
              <input type="time" class="time-input" id="tuesday-end" value="17:00">
            </div>
            <div class="col-md-3">
              <label class="form-label small">Pause (min) :</label>
              <input type="number" class="time-input" id="tuesday-break" value="60" min="0">
            </div>
            <div class="col-md-3">
              <div class="form-check mt-4">
                <input class="form-check-input" type="checkbox" id="tuesday-worked" checked>
                <label class="form-check-label" for="tuesday-worked">Jour travaillé</label>
              </div>
            </div>
          </div>
        </div>

        <!-- Mercredi -->
        <div class="day-input-group">
          <h5><i class="fas fa-calendar-day me-2"></i>Mercredi</h5>
          <div class="row g-2">
            <div class="col-md-3">
              <label class="form-label small">Début :</label>
              <input type="time" class="time-input" id="wednesday-start" value="09:00">
            </div>
            <div class="col-md-3">
              <label class="form-label small">Fin :</label>
              <input type="time" class="time-input" id="wednesday-end" value="17:00">
            </div>
            <div class="col-md-3">
              <label class="form-label small">Pause (min) :</label>
              <input type="number" class="time-input" id="wednesday-break" value="60" min="0">
            </div>
            <div class="col-md-3">
              <div class="form-check mt-4">
                <input class="form-check-input" type="checkbox" id="wednesday-worked" checked>
                <label class="form-check-label" for="wednesday-worked">Jour travaillé</label>
              </div>
            </div>
          </div>
        </div>

        <!-- Jeudi -->
        <div class="day-input-group">
          <h5><i class="fas fa-calendar-day me-2"></i>Jeudi</h5>
          <div class="row g-2">
            <div class="col-md-3">
              <label class="form-label small">Début :</label>
              <input type="time" class="time-input" id="thursday-start" value="09:00">
            </div>
            <div class="col-md-3">
              <label class="form-label small">Fin :</label>
              <input type="time" class="time-input" id="thursday-end" value="17:00">
            </div>
            <div class="col-md-3">
              <label class="form-label small">Pause (min) :</label>
              <input type="number" class="time-input" id="thursday-break" value="60" min="0">
            </div>
            <div class="col-md-3">
              <div class="form-check mt-4">
                <input class="form-check-input" type="checkbox" id="thursday-worked" checked>
                <label class="form-check-label" for="thursday-worked">Jour travaillé</label>
              </div>
            </div>
          </div>
        </div>

        <!-- Vendredi -->
        <div class="day-input-group">
          <h5><i class="fas fa-calendar-day me-2"></i>Vendredi</h5>
          <div class="row g-2">
            <div class="col-md-3">
              <label class="form-label small">Début :</label>
              <input type="time" class="time-input" id="friday-start" value="09:00">
            </div>
            <div class="col-md-3">
              <label class="form-label small">Fin :</label>
              <input type="time" class="time-input" id="friday-end" value="17:00">
            </div>
            <div class="col-md-3">
              <label class="form-label small">Pause (min) :</label>
              <input type="number" class="time-input" id="friday-break" value="60" min="0">
            </div>
            <div class="col-md-3">
              <div class="form-check mt-4">
                <input class="form-check-input" type="checkbox" id="friday-worked" checked>
                <label class="form-check-label" for="friday-worked">Jour travaillé</label>
              </div>
            </div>
          </div>
        </div>

        <!-- Samedi -->
        <div class="day-input-group">
          <h5><i class="fas fa-calendar-day me-2"></i>Samedi</h5>
          <div class="row g-2">
            <div class="col-md-3">
              <label class="form-label small">Début :</label>
              <input type="time" class="time-input" id="saturday-start" value="09:00">
            </div>
            <div class="col-md-3">
              <label class="form-label small">Fin :</label>
              <input type="time" class="time-input" id="saturday-end" value="12:00">
            </div>
            <div class="col-md-3">
              <label class="form-label small">Pause (min) :</label>
              <input type="number" class="time-input" id="saturday-break" value="0" min="0">
            </div>
            <div class="col-md-3">
              <div class="form-check mt-4">
                <input class="form-check-input" type="checkbox" id="saturday-worked">
                <label class="form-check-label" for="saturday-worked">Jour travaillé</label>
              </div>
            </div>
          </div>
        </div>

        <!-- Dimanche -->
        <div class="day-input-group">
          <h5><i class="fas fa-calendar-day me-2"></i>Dimanche</h5>
          <div class="row g-2">
            <div class="col-md-3">
              <label class="form-label small">Début :</label>
              <input type="time" class="time-input" id="sunday-start" value="09:00">
            </div>
            <div class="col-md-3">
              <label class="form-label small">Fin :</label>
              <input type="time" class="time-input" id="sunday-end" value="12:00">
            </div>
            <div class="col-md-3">
              <label class="form-label small">Pause (min) :</label>
              <input type="number" class="time-input" id="sunday-break" value="0" min="0">
            </div>
            <div class="col-md-3">
              <div class="form-check mt-4">
                <input class="form-check-input" type="checkbox" id="sunday-worked">
                <label class="form-check-label" for="sunday-worked">Jour travaillé</label>
              </div>
            </div>
          </div>
        </div>

        <!-- Boutons d'action -->
        <div class="row g-2 mt-4">
          <div class="col-md-6">
            <button onclick="calculateWeek()" class="calculate-week-btn">
              <i class="fas fa-calculator me-2"></i>Calculer la Semaine
            </button>
          </div>
          <div class="col-md-3">
            <button onclick="fillDefaultWeek()" class="btn btn-outline-primary w-100" style="padding: 1rem;">
              <i class="fas fa-magic me-2"></i>Semaine Type
            </button>
          </div>
          <div class="col-md-3">
            <button onclick="resetWeekCalculator()" class="btn btn-secondary w-100" style="padding: 1rem;">
              <i class="fas fa-undo me-2"></i>Réinitialiser
            </button>
          </div>
        </div>

        <!-- Résultats hebdomadaires -->
        <div class="week-results" id="week-results" style="display: none;">
          <h3 class="text-center mb-4"><i class="fas fa-chart-bar me-2"></i>Analyse Hebdomadaire Détaillée</h3>

          <!-- Statistiques principales -->
          <div class="row g-3 mb-4">
            <div class="col-md-3">
              <div class="stats-card">
                <div class="stats-number" id="total-week-hours">--</div>
                <h6>Heures Totales</h6>
              </div>
            </div>
            <div class="col-md-3">
              <div class="stats-card">
                <div class="stats-number" id="work-days-count">--</div>
                <h6>Jours Travaillés</h6>
              </div>
            </div>
            <div class="col-md-3">
              <div class="stats-card">
                <div class="stats-number" id="overtime-week-hours">--</div>
                <h6>Heures Sup.</h6>
              </div>
            </div>
            <div class="col-md-3">
              <div class="stats-card">
                <div class="stats-number" id="week-salary">--</div>
                <h6>Salaire (€)</h6>
              </div>
            </div>
          </div>

          <!-- Tableau détaillé par jour -->
          <div class="chart-container">
            <h4 class="mb-3"><i class="fas fa-table me-2"></i>Détail par Jour</h4>
            <div class="week-summary-table">
              <div class="table-responsive">
                <table class="table table-striped table-hover mb-0" id="week-detail-table">
                  <thead class="table-primary">
                    <tr>
                      <th>Jour</th>
                      <th>Début</th>
                      <th>Fin</th>
                      <th>Pause</th>
                      <th>Heures Travaillées</th>
                      <th>Heures Sup.</th>
                      <th>Salaire</th>
                    </tr>
                  </thead>
                  <tbody id="week-detail-tbody">
                    <!-- Contenu généré dynamiquement -->
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <!-- Graphique de répartition -->
          <div class="chart-container">
            <h4 class="mb-3"><i class="fas fa-chart-pie me-2"></i>Répartition Hebdomadaire</h4>
            <canvas id="weekChart" width="400" height="200"></canvas>
          </div>

          <!-- Boutons d'export -->
          <div class="text-center mt-4">
            <button onclick="exportWeekResults()" class="btn btn-outline-primary me-2">
              <i class="fas fa-download me-2"></i>Exporter PDF
            </button>
            <button onclick="exportWeekExcel()" class="btn btn-outline-success me-2">
              <i class="fas fa-file-excel me-2"></i>Exporter Excel
            </button>
            <button onclick="shareWeekResults()" class="btn btn-outline-info">
              <i class="fas fa-share me-2"></i>Partager
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Méthodologie Section -->
<section id="methodologie" class="py-5 bg-white">
  <div class="container">
    <div class="methodology-section">
      <h2 class="fw-bold text-center mb-5">
        <i class="fas fa-microscope me-2"></i>Méthodologie de Calcul - Calculette Semaine
      </h2>

      <div class="row">
        <div class="col-lg-8">
          <h3 class="h4 mb-3">Expertise et Précision de notre Calculateur Hebdomadaire</h3>
          <p class="lead">
            Notre <strong>calculette semaine</strong> utilise une méthodologie rigoureuse développée par des experts
            en gestion du temps et conforme aux réglementations françaises du travail.
          </p>

          <h4 class="h5 mt-4 mb-3">1. Calcul des Heures Effectives</h4>
          <p>
            Le calcul s'effectue en soustrayant automatiquement les pauses de chaque journée de travail.
            Notre algorithme prend en compte les spécificités de chaque jour et applique la formule :
          </p>
          <div class="alert alert-info">
            <strong>Formule :</strong> Heures Effectives = (Heure Fin - Heure Début) - Temps de Pause
          </div>

          <h4 class="h5 mt-4 mb-3">2. Détection des Heures Supplémentaires</h4>
          <p>
            Conformément au Code du travail français, notre <strong>calculateur hebdomadaire</strong>
            identifie automatiquement les heures supplémentaires au-delà de 35h par semaine
            (ou seuil personnalisable selon votre convention collective).
          </p>

          <h4 class="h5 mt-4 mb-3">3. Calculs Salariaux Avancés</h4>
          <p>
            Si vous renseignez votre taux horaire, la calculette applique automatiquement :
          </p>
          <ul>
            <li>Le taux normal pour les heures régulières</li>
            <li>La majoration de 25% pour les 8 premières heures supplémentaires</li>
            <li>La majoration de 50% au-delà (selon paramétrage)</li>
          </ul>
        </div>

        <div class="col-lg-4">
          <div class="expert-tip">
            <h5><i class="fas fa-award me-2"></i>Certification Qualité</h5>
            <p>
              Notre calculette semaine est validée par des experts-comptables et
              respecte les normes CNIL pour la protection des données.
            </p>
            <ul class="list-unstyled mt-3">
              <li><i class="fas fa-check text-success me-2"></i>Conforme Code du travail</li>
              <li><i class="fas fa-check text-success me-2"></i>Algorithmes certifiés</li>
              <li><i class="fas fa-check text-success me-2"></i>Données sécurisées</li>
              <li><i class="fas fa-check text-success me-2"></i>Calculs vérifiés</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Avantages Professionnels Section -->
<section class="py-5 bg-light">
  <div class="container">
    <div class="text-center mb-5">
      <h2 class="fw-bold">Avantages Professionnels de la Calculette Semaine</h2>
      <p class="lead">Découvrez pourquoi notre calculateur hebdomadaire est plébiscité par les professionnels</p>
    </div>

    <div class="row g-4">
      <div class="col-md-6 col-lg-4">
        <div class="card h-100 shadow-sm feature-card">
          <div class="card-body p-4">
            <div class="mb-3 text-center">
              <i class="fas fa-chart-line text-primary" style="font-size: 3rem;"></i>
            </div>
            <h3 class="h5 mb-3 text-center">Analyse Graphique Avancée</h3>
            <p class="text-muted">
              Visualisez votre répartition hebdomadaire avec des graphiques interactifs
              pour optimiser votre planning de travail.
            </p>
          </div>
        </div>
      </div>

      <div class="col-md-6 col-lg-4">
        <div class="card h-100 shadow-sm feature-card">
          <div class="card-body p-4">
            <div class="mb-3 text-center">
              <i class="fas fa-file-export text-primary" style="font-size: 3rem;"></i>
            </div>
            <h3 class="h5 mb-3 text-center">Export Multi-formats</h3>
            <p class="text-muted">
              Exportez vos résultats en PDF, Excel ou CSV pour intégration
              dans vos outils de gestion RH ou comptabilité.
            </p>
          </div>
        </div>
      </div>

      <div class="col-md-6 col-lg-4">
        <div class="card h-100 shadow-sm feature-card">
          <div class="card-body p-4">
            <div class="mb-3 text-center">
              <i class="fas fa-shield-alt text-primary" style="font-size: 3rem;"></i>
            </div>
            <h3 class="h5 mb-3 text-center">Conformité Réglementaire</h3>
            <p class="text-muted">
              Respect automatique des réglementations françaises du travail
              et des conventions collectives principales.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- FAQ Section -->
<section class="py-5 bg-white">
  <div class="container">
    <div class="text-center mb-5">
      <h2 class="fw-bold">Questions Fréquentes - Calculette Semaine</h2>
      <p class="lead">Trouvez les réponses aux questions les plus courantes sur notre calculateur hebdomadaire</p>
    </div>

    <div class="row justify-content-center">
      <div class="col-lg-10">
        <div class="accordion" id="faqWeekAccordion">
          <div class="accordion-item shadow-sm mb-3">
            <h3 class="accordion-header" id="headingWeekOne">
              <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseWeekOne" aria-expanded="true" aria-controls="collapseWeekOne">
                Comment fonctionne la Calculette Semaine exactement ?
              </button>
            </h3>
            <div id="collapseWeekOne" class="accordion-collapse collapse show" aria-labelledby="headingWeekOne" data-bs-parent="#faqWeekAccordion">
              <div class="accordion-body">
                <p>La <strong>Calculette Semaine</strong> analyse jour par jour vos horaires de travail pour calculer précisément votre temps de travail hebdomadaire. Elle prend en compte les heures de début et fin, les pauses, et génère automatiquement des statistiques détaillées.</p>
                <p>Notre calculateur hebdomadaire utilise des algorithmes avancés pour détecter les heures supplémentaires et calculer les salaires selon les réglementations françaises.</p>
              </div>
            </div>
          </div>

          <div class="accordion-item shadow-sm mb-3">
            <h3 class="accordion-header" id="headingWeekTwo">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseWeekTwo" aria-expanded="false" aria-controls="collapseWeekTwo">
                Puis-je exporter mes résultats de calcul hebdomadaire ?
              </button>
            </h3>
            <div id="collapseWeekTwo" class="accordion-collapse collapse" aria-labelledby="headingWeekTwo" data-bs-parent="#faqWeekAccordion">
              <div class="accordion-body">
                <p>Oui, notre <strong>calculette semaine</strong> propose plusieurs formats d'export :</p>
                <ul>
                  <li><strong>PDF</strong> : Rapport détaillé avec graphiques pour présentation</li>
                  <li><strong>Excel</strong> : Tableau de données pour analyse approfondie</li>
                  <li><strong>CSV</strong> : Format compatible avec tous les logiciels de gestion</li>
                </ul>
                <p>Ces exports incluent tous les détails : heures par jour, pauses, heures supplémentaires et calculs salariaux.</p>
              </div>
            </div>
          </div>

          <div class="accordion-item shadow-sm mb-3">
            <h3 class="accordion-header" id="headingWeekThree">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseWeekThree" aria-expanded="false" aria-controls="collapseWeekThree">
                La Calculette Semaine respecte-t-elle la réglementation française ?
              </button>
            </h3>
            <div id="collapseWeekThree" class="accordion-collapse collapse" aria-labelledby="headingWeekThree" data-bs-parent="#faqWeekAccordion">
              <div class="accordion-body">
                <p>Absolument. Notre <strong>calculateur hebdomadaire</strong> est développé en conformité avec :</p>
                <ul>
                  <li>Le Code du travail français (durée légale de 35h)</li>
                  <li>Les majorations d'heures supplémentaires (25% puis 50%)</li>
                  <li>Les conventions collectives principales</li>
                  <li>Les normes CNIL pour la protection des données</li>
                </ul>
                <p>Nos algorithmes sont régulièrement mis à jour pour suivre les évolutions réglementaires.</p>
              </div>
            </div>
          </div>

          <div class="accordion-item shadow-sm">
            <h3 class="accordion-header" id="headingWeekFour">
              <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseWeekFour" aria-expanded="false" aria-controls="collapseWeekFour">
                Mes données sont-elles sécurisées avec la Calculette Semaine ?
              </button>
            </h3>
            <div id="collapseWeekFour" class="accordion-collapse collapse" aria-labelledby="headingWeekFour" data-bs-parent="#faqWeekAccordion">
              <div class="accordion-body">
                <p>La sécurité est notre priorité. Notre <strong>calculette semaine</strong> :</p>
                <ul>
                  <li>Ne stocke aucune donnée personnelle sur nos serveurs</li>
                  <li>Effectue tous les calculs localement dans votre navigateur</li>
                  <li>Utilise le chiffrement HTTPS pour toutes les communications</li>
                  <li>Respecte le RGPD et les normes de confidentialité</li>
                </ul>
                <p>Vos horaires de travail restent strictement confidentiels et sous votre contrôle.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- CTA Section -->
<section class="py-5 bg-primary text-white">
  <div class="container">
    <div class="row py-3 justify-content-center text-center">
      <div class="col-lg-8">
        <h2 class="fw-bold mb-3">Optimisez votre gestion du temps avec la Calculette Semaine</h2>
        <p class="lead mb-4">
          Utilisez notre calculateur hebdomadaire professionnel pour analyser précisément
          vos heures de travail et optimiser votre productivité !
        </p>
        <a href="#calculette-semaine" class="btn btn-light btn-lg px-4 py-2">
          <i class="fas fa-calculator me-2"></i>Calculer ma semaine maintenant
        </a>
      </div>
    </div>
  </div>
</section>
{% endblock %}

{% block extra_js %}
<!-- Chart.js pour les graphiques -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// Variables globales
let weekChart = null;
let weekData = {};

// Fonction principale de calcul hebdomadaire
function calculateWeek() {
  const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
  const dayNames = ['Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi', 'Dimanche'];
  const hourlyRate = parseFloat(document.getElementById('hourly-rate-week').value) || 0;

  let totalHours = 0;
  let totalMinutes = 0;
  let workDaysCount = 0;
  let totalOvertimeHours = 0;
  let totalSalary = 0;

  weekData = {
    days: [],
    totalHours: 0,
    workDaysCount: 0,
    overtimeHours: 0,
    totalSalary: 0
  };

  // Calcul pour chaque jour
  days.forEach((day, index) => {
    const worked = document.getElementById(`${day}-worked`).checked;

    if (worked) {
      const startTime = document.getElementById(`${day}-start`).value;
      const endTime = document.getElementById(`${day}-end`).value;
      const breakMinutes = parseInt(document.getElementById(`${day}-break`).value) || 0;

      if (startTime && endTime) {
        const start = new Date(`2024-01-01T${startTime}`);
        const end = new Date(`2024-01-01T${endTime}`);

        let diffMs = end - start;
        if (diffMs < 0) {
          diffMs += 24 * 60 * 60 * 1000; // Jour suivant
        }

        const dayMinutes = Math.floor(diffMs / (1000 * 60)) - breakMinutes;
        const dayHours = dayMinutes / 60;

        // Calcul des heures supplémentaires (au-delà de 7h par jour)
        const dailyOvertimeHours = Math.max(0, dayHours - 7);
        const regularDayHours = Math.min(dayHours, 7);

        let daySalary = 0;
        if (hourlyRate > 0) {
          daySalary = (regularDayHours * hourlyRate) + (dailyOvertimeHours * hourlyRate * 1.25);
        }

        weekData.days.push({
          name: dayNames[index],
          startTime,
          endTime,
          breakMinutes,
          hours: dayHours,
          overtimeHours: dailyOvertimeHours,
          salary: daySalary
        });

        totalHours += dayHours;
        totalMinutes += dayMinutes;
        totalOvertimeHours += dailyOvertimeHours;
        totalSalary += daySalary;
        workDaysCount++;
      }
    }
  });

  // Calcul des heures supplémentaires hebdomadaires (au-delà de 35h)
  const weeklyOvertimeHours = Math.max(0, totalHours - 35);

  // Mise à jour des statistiques
  weekData.totalHours = totalHours;
  weekData.workDaysCount = workDaysCount;
  weekData.overtimeHours = weeklyOvertimeHours;
  weekData.totalSalary = totalSalary;

  // Affichage des résultats
  document.getElementById('total-week-hours').textContent = totalHours.toFixed(1) + 'h';
  document.getElementById('work-days-count').textContent = workDaysCount;
  document.getElementById('overtime-week-hours').textContent = weeklyOvertimeHours.toFixed(1) + 'h';
  document.getElementById('week-salary').textContent = hourlyRate > 0 ? totalSalary.toFixed(2) + '€' : '--';

  // Génération du tableau détaillé
  generateWeekTable();

  // Génération du graphique
  generateWeekChart();

  // Affichage des résultats
  document.getElementById('week-results').style.display = 'block';

  // Scroll vers les résultats
  document.getElementById('week-results').scrollIntoView({ behavior: 'smooth' });
}

// Génération du tableau détaillé
function generateWeekTable() {
  const tbody = document.getElementById('week-detail-tbody');
  tbody.innerHTML = '';

  weekData.days.forEach(day => {
    const row = tbody.insertRow();
    row.innerHTML = `
      <td><strong>${day.name}</strong></td>
      <td>${day.startTime}</td>
      <td>${day.endTime}</td>
      <td>${day.breakMinutes}min</td>
      <td>${day.hours.toFixed(2)}h</td>
      <td>${day.overtimeHours.toFixed(2)}h</td>
      <td>${day.salary > 0 ? day.salary.toFixed(2) + '€' : '--'}</td>
    `;
  });
}

// Génération du graphique
function generateWeekChart() {
  const ctx = document.getElementById('weekChart').getContext('2d');

  if (weekChart) {
    weekChart.destroy();
  }

  const labels = weekData.days.map(day => day.name);
  const data = weekData.days.map(day => day.hours);

  weekChart = new Chart(ctx, {
    type: 'bar',
    data: {
      labels: labels,
      datasets: [{
        label: 'Heures travaillées',
        data: data,
        backgroundColor: 'rgba(54, 162, 235, 0.6)',
        borderColor: 'rgba(54, 162, 235, 1)',
        borderWidth: 1
      }]
    },
    options: {
      responsive: true,
      scales: {
        y: {
          beginAtZero: true,
          title: {
            display: true,
            text: 'Heures'
          }
        }
      },
      plugins: {
        title: {
          display: true,
          text: 'Répartition des heures par jour'
        }
      }
    }
  });
}

// Remplissage automatique semaine type
function fillDefaultWeek() {
  const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'];

  days.forEach(day => {
    document.getElementById(`${day}-start`).value = '09:00';
    document.getElementById(`${day}-end`).value = '17:00';
    document.getElementById(`${day}-break`).value = '60';
    document.getElementById(`${day}-worked`).checked = true;
  });

  // Weekend
  document.getElementById('saturday-worked').checked = false;
  document.getElementById('sunday-worked').checked = false;

  // Date de début de semaine (lundi de cette semaine)
  const today = new Date();
  const monday = new Date(today.setDate(today.getDate() - today.getDay() + 1));
  document.getElementById('week-start-date').value = monday.toISOString().split('T')[0];
}

// Réinitialisation
function resetWeekCalculator() {
  const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];

  days.forEach(day => {
    document.getElementById(`${day}-start`).value = '09:00';
    document.getElementById(`${day}-end`).value = '17:00';
    document.getElementById(`${day}-break`).value = '60';
    document.getElementById(`${day}-worked`).checked = false;
  });

  document.getElementById('hourly-rate-week').value = '';
  document.getElementById('default-break').value = '60';
  document.getElementById('week-start-date').value = '';

  document.getElementById('week-results').style.display = 'none';

  if (weekChart) {
    weekChart.destroy();
    weekChart = null;
  }
}

// Export PDF
function exportWeekResults() {
  if (!weekData.days || weekData.days.length === 0) {
    alert('Veuillez d\'abord effectuer un calcul');
    return;
  }

  // Simulation d'export PDF (nécessiterait une vraie librairie PDF)
  alert('Fonctionnalité d\'export PDF en cours de développement');
}

// Export Excel
function exportWeekExcel() {
  if (!weekData.days || weekData.days.length === 0) {
    alert('Veuillez d\'abord effectuer un calcul');
    return;
  }

  let csvContent = "Jour,Début,Fin,Pause (min),Heures Travaillées,Heures Supplémentaires,Salaire\n";

  weekData.days.forEach(day => {
    csvContent += `${day.name},${day.startTime},${day.endTime},${day.breakMinutes},${day.hours.toFixed(2)},${day.overtimeHours.toFixed(2)},${day.salary.toFixed(2)}\n`;
  });

  csvContent += `\nTOTAL,,,,${weekData.totalHours.toFixed(2)},${weekData.overtimeHours.toFixed(2)},${weekData.totalSalary.toFixed(2)}`;

  const blob = new Blob([csvContent], { type: 'text/csv' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `calculette-semaine-${new Date().toISOString().split('T')[0]}.csv`;
  a.click();
  URL.revokeObjectURL(url);
}

// Partage des résultats
function shareWeekResults() {
  if (!weekData.days || weekData.days.length === 0) {
    alert('Veuillez d\'abord effectuer un calcul');
    return;
  }

  const shareText = `J'ai analysé ma semaine de travail avec la Calculette Semaine : ${weekData.totalHours.toFixed(1)}h sur ${weekData.workDaysCount} jours !`;

  if (navigator.share) {
    navigator.share({
      title: 'Mes résultats Calculette Semaine',
      text: shareText,
      url: window.location.href
    });
  } else {
    navigator.clipboard.writeText(shareText + ' ' + window.location.href)
      .then(() => {
        alert('Résultats copiés dans le presse-papier !');
      });
  }
}

// Initialisation au chargement de la page
document.addEventListener('DOMContentLoaded', function() {
  // Remplir la date de début de semaine par défaut
  const today = new Date();
  const monday = new Date(today.setDate(today.getDate() - today.getDay() + 1));
  document.getElementById('week-start-date').value = monday.toISOString().split('T')[0];

  // Smooth scrolling pour les liens d'ancrage
  const anchorLinks = document.querySelectorAll('a[href^="#"]');
  anchorLinks.forEach(link => {
    link.addEventListener('click', function(e) {
      e.preventDefault();
      const targetId = this.getAttribute('href').substring(1);
      const targetElement = document.getElementById(targetId);
      if (targetElement) {
        targetElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    });
  });
});
</script>
{% endblock %}
